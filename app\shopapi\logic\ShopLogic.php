<?php
namespace app\shopapi\logic;

use app\common\basics\Logic;
use app\common\server\JsonServer;
use think\facade\Db;

class ShopLogic extends Logic
{
    public static function freeEntry($data)
    {
        // 在这里添加您的免费入驻逻辑

        // 触发通知
        $notificationData = [
            'title' => '新的免费入驻申请',
            'content' => '您有一条新的免费入驻申请，请及时处理。',
            'type' => 'admin_notification',
            'url' => '/admin/shop_entry/list',
        ];

        $redis = \think\facade\Cache::store('redis');
        $redis->publish('admin_notification', json_encode([
            'event' => 'admin_notification',
            'data' => $notificationData
        ]));

        return JsonServer::success('申请成功');
    }
}
