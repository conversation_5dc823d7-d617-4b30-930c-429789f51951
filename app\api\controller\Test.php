<?php

namespace app\api\controller;

use app\admin\logic\WechatMerchantTransferLogic;
use app\common\basics\Api;
use app\common\model\WithdrawApply;
use app\api\controller\Notification; // 引入Notification控制器
use app\api\controller\Websocket; // 引入Websocket控制器
use think\facade\Request; // 引入Request类
use think\facade\Log; // 引入日志类


/**
 * 测试
 * Class Test
 * @package app\api\controller
 */
class Test extends Api
{
    public $like_not_need_login = ['test', 'testSseNotification', 'testWebsocketNotification'];

    public function test()
    {
        $withdraw = WithdrawApply::where('id', 111)->findOrEmpty()->toArray();
//        $result = WechatMerchantTransferLogic::transfer($withdraw);
        $result = WechatMerchantTransferLogic::details($withdraw);
        halt($result);

    }

    /**
     * 测试SSE通知接口
     * @ApiTitle (测试SSE通知)
     * @ApiSummary (用于测试向后台推送SSE通知)
     * @ApiMethod (GET)
     * @ApiParams (
     *   {"name":"title", "type":"string", "require":false, "desc":"通知标题，默认为'测试SSE通知'"},
     *   {"name":"content", "type":"string", "require":false, "desc":"通知内容，默认为'这是一条来自测试接口的通知'"},
     *   {"name":"type", "type":"string", "require":false, "desc":"通知类型，默认为'test_notification'"},
     *   {"name":"url", "type":"string", "require":false, "desc":"点击通知跳转的URL，默认为空"},
     *   {"name":"icon", "type":"integer", "require":false, "desc":"通知图标，默认为0"}
     * )
     * @ApiReturn ({"code":1,"msg":"成功","data":null})
     */
    public function testSseNotification()
    {
        $title = Request::get('title', '测试SSE通知');
        $content = Request::get('content', '这是一条来自测试接口的通知');
        $type = Request::get('type', 'test_notification');
        $url = Request::get('url', '');
        $icon = Request::get('icon', 0, 'intval');

        $notification = new Notification(app()); // 传入app()对象
        $notification->sendToAdmin([
            'title' => $title,
            'content' => $content,
            'url' => $url,
            'type' => $type,
            'icon' => $icon
        ]);

        return \app\common\server\JsonServer::success('测试SSE通知已发送');
    }

    /**
     * 测试WebSocket通知接口
     * @ApiTitle (测试WebSocket通知)
     * @ApiSummary (用于测试向admin和shop推送WebSocket通知)
     * @ApiMethod (GET|POST)
     * @ApiParams (
     *   {"name":"target", "type":"string", "require":false, "desc":"推送目标：admin, shop, all，默认为'all'"},
     *   {"name":"message", "type":"string", "require":false, "desc":"自定义消息内容，默认为'这是一条WebSocket测试通知'"},
     *   {"name":"title", "type":"string", "require":false, "desc":"通知标题，默认为'WebSocket测试通知'"},
     *   {"name":"type", "type":"string", "require":false, "desc":"消息类型：order, chat, system, test_notification，默认为'test_notification'"},
     *   {"name":"url", "type":"string", "require":false, "desc":"点击通知跳转的URL，默认为空"},
     *   {"name":"icon", "type":"integer", "require":false, "desc":"通知图标：0-默认，1-成功，2-错误，3-警告，4-信息，默认为0"}
     * )
     * @ApiReturn ({"code":1,"msg":"WebSocket通知测试完成","data":{"push_results":{},"total_connections":0,"test_data":{}}})
     */
    public function testWebsocketNotification()
    {
        // 获取请求参数，支持GET和POST
        $target = Request::param('target', 'all');
        $message = Request::param('message', '这是一条WebSocket测试通知');
        $title = Request::param('title', 'WebSocket测试通知');
        $type = Request::param('type', 'test_notification');
        $url = Request::param('url', '');
        $icon = Request::param('icon', 0, 'intval');

        // 记录测试请求日志
        Log::info('WebSocket通知测试请求: ' . json_encode([
            'target' => $target,
            'title' => $title,
            'message' => $message,
            'type' => $type,
            'url' => $url,
            'icon' => $icon
        ], JSON_UNESCAPED_UNICODE));

        // 参数验证
        $validTargets = ['admin', 'shop', 'all'];
        if (!in_array($target, $validTargets)) {
            return \app\common\server\JsonServer::error('无效的target参数，支持的值：' . implode(', ', $validTargets));
        }

        $validTypes = ['order', 'chat', 'system', 'test_notification'];
        if (!in_array($type, $validTypes)) {
            return \app\common\server\JsonServer::error('无效的type参数，支持的值：' . implode(', ', $validTypes));
        }

        if ($icon < 0 || $icon > 4) {
            return \app\common\server\JsonServer::error('无效的icon参数，支持的值：0-4');
        }

        // 构建测试数据，根据消息类型进行增强
        $testData = $this->buildTestDataByType($title, $message, $type, $url, $icon);

        // 初始化推送结果
        $pushResults = [];
        $totalConnections = 0;
      
        try {
            // 创建Websocket控制器实例
            $websocketController = new Websocket(app());
            
            // 同时尝试通过Notification控制器发送（作为备用方案）
            $notificationController = new Notification(app());

            // 根据target参数决定推送目标
            if ($target === 'admin' || $target === 'all') {
                // 推送给admin
                $adminResult = $this->pushToAdmin($websocketController, $testData);

                $pushResults['admin'] = $adminResult;
                $totalConnections += $adminResult['connections'];
            }

            if ($target === 'shop' || $target === 'all') {
                // 推送给shop
                $shopResult = $this->pushToShop($websocketController, $testData);
                $pushResults['shop'] = $shopResult;
                $totalConnections += $shopResult['connections'];
            }

            // 构建响应数据
            $responseData = [
                'push_results' => $pushResults,
                'total_connections' => $totalConnections,
                'test_data' => $testData
            ];

            Log::info('WebSocket通知测试完成: ' . json_encode($responseData, JSON_UNESCAPED_UNICODE));

            return \app\common\server\JsonServer::success('WebSocket通知测试完成', $responseData);

        } catch (\Exception $e) {
            Log::error('WebSocket通知测试失败: ' . $e->getMessage());
            return \app\common\server\JsonServer::error('WebSocket通知测试失败: ' . $e->getMessage());
        }
    }

    /**
     * 推送通知给admin
     * @param Websocket $websocketController
     * @param array $testData
     * @return array
     */
    private function pushToAdmin($websocketController, $testData)
    {
        try {
            Log::info('开始推送admin通知: ' . json_encode($testData, JSON_UNESCAPED_UNICODE));
            
            // 构建通知数据
            $adminNotificationData = [
                'title' => $testData['title'],
                'content' => $testData['content'],
                'type' => $testData['type'],
                'url' => $testData['url'],
                'icon' => $testData['icon']
            ];

            // 方法1: 直接通过CombinedHandler推送（最直接的方法）
            $eventResult = $this->pushViaCombinedHandler('admin', $testData);
            
            // 方法2: 通过WebSocket控制器推送（备用方法）
            $websocketResult = $this->pushViaWebsocketController('admin_group', $testData);
            
            // 方法3: 通过Redis推送（备用方案）
            $redisResult = $this->pushViaRedis('admin_notifications', $testData);
            
            // 获取连接数统计
            $connectionCount = $this->getAdminConnectionCount();
            
            // 判断推送结果
            if ($eventResult['success'] || $websocketResult['success'] || $redisResult['success']) {
                Log::info('Admin通知推送成功 - Event: ' . ($eventResult['success'] ? '成功' : '失败') . 
                         ', WebSocket: ' . ($websocketResult['success'] ? '成功' : '失败') . 
                         ', Redis: ' . ($redisResult['success'] ? '成功' : '失败'));
                
                return [
                    'success' => true,
                    'connections' => $connectionCount,
                    'message' => "已推送给admin模块 (连接数: {$connectionCount})",
                    'methods' => [
                        'event' => $eventResult,
                        'websocket' => $websocketResult,
                        'redis' => $redisResult
                    ],
                    'push_details' => [
                        'event_success' => $eventResult['success'],
                        'websocket_success' => $websocketResult['success'],
                        'redis_success' => $redisResult['success'],
                        'total_methods' => ($eventResult['success'] ? 1 : 0) + ($websocketResult['success'] ? 1 : 0) + ($redisResult['success'] ? 1 : 0)
                    ]
                ];
            } else {
                Log::error('Admin通知推送失败 - Event: ' . $eventResult['error'] . 
                          ', WebSocket: ' . $websocketResult['error'] . 
                          ', Redis: ' . $redisResult['error']);
                
                return [
                    'success' => false,
                    'connections' => $connectionCount,
                    'message' => '推送给admin失败',
                    'error' => 'Event: ' . $eventResult['error'] . '; WebSocket: ' . $websocketResult['error'] . '; Redis: ' . $redisResult['error'],
                    'methods' => [
                        'event' => $eventResult,
                        'websocket' => $websocketResult,
                        'redis' => $redisResult
                    ]
                ];
            }
        } catch (\Exception $e) {
            Log::error('推送给admin异常: ' . $e->getMessage());
            return [
                'success' => false,
                'connections' => 0,
                'message' => '推送给admin异常: ' . $e->getMessage(),
                'error' => $e->getMessage(),
                'method' => 'exception'
            ];
        }
    }

    /**
     * 通过现有WebSocket控制器推送消息
     * @param string $group 目标组名
     * @param array $testData 测试数据
     * @return array
     */
    private function pushViaWebsocketController($group, $testData)
    {
        try {
            Log::info('尝试通过WebSocket控制器推送到组: ' . $group);
            
            // 构建通知数据
            $notificationData = [
                'title' => $testData['title'],
                'content' => $testData['content'],
                'type' => $testData['type'],
                'url' => $testData['url'],
                'icon' => $testData['icon']
            ];

            // 创建Websocket控制器实例
            $websocketController = new Websocket(app());
            
            // 临时设置POST数据
            $originalPost = $_POST;
            $_POST = $notificationData;
            
            $result = null;
            
            // 根据目标组选择推送方法
            if ($group === 'admin_group') {
                $result = $websocketController->sendAdminNotification();
            } else if ($group === 'shop_group') {
                // 为shop设置receive_type
                $_POST['receive_type'] = 2; // 商家
                $_POST['user_id'] = 0; // 系统通知
                $result = $websocketController->sendNotification();
            }
            
            // 恢复原始POST数据
            $_POST = $originalPost;
            
            // 检查推送结果
            if ($result && method_exists($result, 'getData')) {
                $resultData = $result->getData();
                if (isset($resultData['code']) && $resultData['code'] == 1) {
                    Log::info('WebSocket控制器推送成功');
                    return [
                        'success' => true,
                        'error' => null,
                        'method' => 'websocket_controller',
                        'group' => $group,
                        'message_sent' => $notificationData
                    ];
                } else {
                    $errorMsg = isset($resultData['msg']) ? $resultData['msg'] : '未知错误';
                    Log::warning('WebSocket控制器推送失败: ' . $errorMsg);
                    return [
                        'success' => false,
                        'error' => 'WebSocket控制器推送失败: ' . $errorMsg,
                        'method' => 'websocket_controller'
                    ];
                }
            } else {
                Log::warning('WebSocket控制器返回无效结果');
                return [
                    'success' => false,
                    'error' => 'WebSocket控制器返回无效结果',
                    'method' => 'websocket_controller'
                ];
            }
            
        } catch (\Exception $e) {
            Log::error('WebSocket控制器推送异常: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'WebSocket控制器推送异常: ' . $e->getMessage(),
                'method' => 'websocket_controller'
            ];
        }
    }

    /**
     * 通过Redis推送消息
     * @param string $channel Redis频道名
     * @param array $testData 测试数据
     * @return array
     */
    private function pushViaRedis($channel, $testData)
    {
        try {
            $redis = \think\facade\Cache::store('redis');
            
            // 构建Redis消息格式
            $redisMessage = [
                'event' => 'admin_notification',
                'data' => [
                    'type' => $testData['type'],
                    'title' => $testData['title'],
                    'content' => $testData['content'],
                    'url' => $testData['url'],
                    'icon' => $testData['icon'],
                    'timestamp' => $testData['timestamp'],
                    'test_mode' => true,
                    'test_id' => $testData['test_id']
                ]
            ];

            $messageJson = json_encode($redisMessage, JSON_UNESCAPED_UNICODE);
            
            Log::info('尝试通过Redis推送到频道: ' . $channel);
            
            // 方法1: 发布到Redis频道
            $publishResult = false;
            if (method_exists($redis, 'publish')) {
                $publishResult = $redis->publish($channel, $messageJson);
                Log::info('Redis发布结果: ' . ($publishResult ? '成功' : '失败'));
            }
            
            // 方法2: 存储到Redis键（备用方案）
            $setResult = $redis->set('latest_' . $channel, $messageJson, 300); // 5分钟过期
            Log::info('Redis存储结果: ' . ($setResult ? '成功' : '失败'));
            
            // 方法3: 添加到通知队列
            $queueKey = $channel . '_queue';
            $notificationKey = $channel . '_' . time() . '_' . mt_rand(1000, 9999);
            $redis->set($notificationKey, $messageJson, 300);
            
            // 获取现有队列
            $queueData = $redis->get($queueKey);
            $queue = [];
            if (!empty($queueData)) {
                $decoded = json_decode($queueData, true);
                if (is_array($decoded)) {
                    $queue = $decoded;
                }
            }
            
            // 添加到队列
            $queue[] = $notificationKey;
            
            // 限制队列长度
            if (count($queue) > 50) {
                $oldKey = array_shift($queue);
                if ($oldKey) {
                    $redis->delete($oldKey);
                }
            }
            
            // 更新队列
            $queueResult = $redis->set($queueKey, json_encode($queue, JSON_UNESCAPED_UNICODE), 300);
            Log::info('Redis队列更新结果: ' . ($queueResult ? '成功' : '失败') . ', 队列长度: ' . count($queue));
            
            // 判断整体推送结果
            $success = $publishResult || $setResult || $queueResult;
            
            return [
                'success' => $success,
                'error' => $success ? null : 'Redis推送失败',
                'method' => 'redis',
                'channel' => $channel,
                'results' => [
                    'publish' => $publishResult,
                    'set' => $setResult,
                    'queue' => $queueResult,
                    'queue_length' => count($queue)
                ],
                'message_sent' => $redisMessage
            ];
            
        } catch (\Exception $e) {
            Log::error('Redis推送异常: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Redis推送异常: ' . $e->getMessage(),
                'method' => 'redis'
            ];
        }
    }

    /**
     * 推送通知给shop
     * @param Websocket $websocketController
     * @param array $testData
     * @return array
     */
    private function pushToShop($websocketController, $testData)
    {
        try {
            Log::info('开始推送shop通知: ' . json_encode($testData, JSON_UNESCAPED_UNICODE));
            
            // 方法1: 通过WebSocket控制器推送（主要方法）
            $websocketResult = $this->pushViaWebsocketController('shop_group', $testData);
            
            // 方法2: 通过Redis推送（备用方案）
            $redisResult = $this->pushViaRedis('shop_notifications', $testData);
            
            // 获取连接数统计
            $connectionCount = $this->getShopConnectionCount();
            
            // 判断推送结果
            if ($websocketResult['success'] || $redisResult['success']) {
                Log::info('Shop通知推送成功 - WebSocket: ' . ($websocketResult['success'] ? '成功' : '失败') . 
                         ', Redis: ' . ($redisResult['success'] ? '成功' : '失败'));
                
                return [
                    'success' => true,
                    'connections' => $connectionCount,
                    'message' => "已推送给shop模块 (连接数: {$connectionCount})",
                    'methods' => [
                        'websocket' => $websocketResult,
                        'redis' => $redisResult
                    ],
                    'push_details' => [
                        'websocket_success' => $websocketResult['success'],
                        'redis_success' => $redisResult['success'],
                        'total_methods' => ($websocketResult['success'] ? 1 : 0) + ($redisResult['success'] ? 1 : 0)
                    ]
                ];
            } else {
                Log::error('Shop通知推送失败 - WebSocket: ' . $websocketResult['error'] . 
                          ', Redis: ' . $redisResult['error']);
                
                return [
                    'success' => false,
                    'connections' => $connectionCount,
                    'message' => '推送给shop失败',
                    'error' => 'WebSocket: ' . $websocketResult['error'] . '; Redis: ' . $redisResult['error'],
                    'methods' => [
                        'websocket' => $websocketResult,
                        'redis' => $redisResult
                    ]
                ];
            }
        } catch (\Exception $e) {
            Log::error('推送给shop异常: ' . $e->getMessage());
            return [
                'success' => false,
                'connections' => 0,
                'message' => '推送给shop异常: ' . $e->getMessage(),
                'error' => $e->getMessage(),
                'method' => 'exception'
            ];
        }
    }

    /**
     * 获取admin连接数统计
     * @return int
     */
    private function getAdminConnectionCount()
    {
        try {
            // 通过Redis检查admin连接数
            $redis = \think\facade\Cache::store('redis');
            $activeConnections = 0;
            
            // 获取WebSocket前缀配置
            $prefix = config('default.websocket_prefix', 'socket_');
            
            // 方法1: 检查admin_*键中的连接
            $adminKeys = $redis->keys($prefix . 'admin_*');
            if (is_array($adminKeys)) {
                foreach ($adminKeys as $key) {
                    $value = $redis->get($key);
                    if ($value && is_numeric($value) && $value > 0) {
                        $activeConnections++;
                    }
                }
            }
            
            // 方法2: 如果上面没找到，检查fd_*键中的admin类型连接
            if ($activeConnections === 0) {
                $fdKeys = $redis->keys($prefix . 'fd_*');
                if (is_array($fdKeys)) {
                    foreach ($fdKeys as $fdKey) {
                        $fdData = $redis->get($fdKey);
                        if ($fdData) {
                            $data = json_decode($fdData, true);
                            if ($data && isset($data['type']) && $data['type'] === 'admin') {
                                $activeConnections++;
                            }
                        }
                    }
                }
            }
            
            Log::info("检测到 {$activeConnections} 个admin连接");
            return $activeConnections;
        } catch (\Exception $e) {
            Log::warning('获取admin连接数失败: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 获取shop连接数统计
     * @return int
     */
    private function getShopConnectionCount()
    {
        try {
            // 通过Redis检查shop连接数
            $redis = \think\facade\Cache::store('redis');
            $activeConnections = 0;
            
            // 获取WebSocket前缀配置
            $prefix = config('default.websocket_prefix', 'socket_');
            
            // 方法1: 检查shop_*键中的连接
            $shopKeys = $redis->keys($prefix . 'shop_*');
            if (is_array($shopKeys)) {
                foreach ($shopKeys as $key) {
                    $value = $redis->get($key);
                    if ($value && is_numeric($value) && $value > 0) {
                        $activeConnections++;
                    }
                }
            }
            
            // 方法2: 如果上面没找到，检查kefu相关的连接
            if ($activeConnections === 0) {
                $kefuKeys = $redis->keys($prefix . 'kefu_*');
                if (is_array($kefuKeys)) {
                    foreach ($kefuKeys as $key) {
                        $value = $redis->get($key);
                        if ($value && is_numeric($value) && $value > 0) {
                            $activeConnections++;
                        }
                    }
                }
            }
            
            // 方法3: 如果还没找到，检查fd_*键中的shop/kefu类型连接
            if ($activeConnections === 0) {
                $fdKeys = $redis->keys($prefix . 'fd_*');
                if (is_array($fdKeys)) {
                    foreach ($fdKeys as $fdKey) {
                        $fdData = $redis->get($fdKey);
                        if ($fdData) {
                            $data = json_decode($fdData, true);
                            if ($data && isset($data['type']) && ($data['type'] === 'shop' || $data['type'] === 'kefu')) {
                                $activeConnections++;
                            }
                        }
                    }
                }
            }
            
            Log::info("检测到 {$activeConnections} 个shop连接");
            return $activeConnections;
        } catch (\Exception $e) {
            Log::warning('获取shop连接数失败: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 直接通过CombinedHandler推送通知
     * @param string $target 目标类型 (admin/shop)
     * @param array $testData 测试数据
     * @return array
     */
    private function pushViaCombinedHandler($target, $testData)
    {
        try {
            Log::info('尝试通过CombinedHandler推送通知: ' . $target);
            
            // 检查CombinedHandler是否存在
            if (!class_exists('\app\common\websocket\CombinedHandler')) {
                return [
                    'success' => false,
                    'error' => 'CombinedHandler类不存在',
                    'method' => 'combined_handler'
                ];
            }
            
            // 尝试获取WebSocket服务器实例
            // 这里我们通过静态方法或全局变量来获取实例
            $handlerInstance = $this->getWebSocketHandlerInstance();
            
            if (!$handlerInstance) {
                // 如果无法获取实例，尝试通过Redis直接推送
                return $this->pushViaDirectRedis($target, $testData);
            }
            
            if ($target === 'admin') {
                // 使用CombinedHandler的sendNotificationToAdmin方法
                $result = $handlerInstance->sendNotificationToAdmin(
                    $testData['title'],
                    $testData['content'],
                    $testData['type'],
                    $testData['url'],
                    $testData['icon']
                );
                
                if ($result) {
                    Log::info('CombinedHandler admin通知推送成功');
                    return [
                        'success' => true,
                        'error' => null,
                        'method' => 'combined_handler',
                        'target' => 'admin',
                        'data_sent' => $testData
                    ];
                } else {
                    return [
                        'success' => false,
                        'error' => 'CombinedHandler推送失败',
                        'method' => 'combined_handler'
                    ];
                }
            } else {
                // 对于shop，我们需要使用不同的方法
                // 暂时返回不支持
                return [
                    'success' => false,
                    'error' => 'Shop推送暂不支持CombinedHandler方法',
                    'method' => 'combined_handler'
                ];
            }
            
        } catch (\Exception $e) {
            Log::error('CombinedHandler推送异常: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'CombinedHandler推送异常: ' . $e->getMessage(),
                'method' => 'combined_handler'
            ];
        }
    }

    /**
     * 获取WebSocket处理器实例
     * @return mixed|null
     */
    private function getWebSocketHandlerInstance()
    {
        try {
            // 尝试从容器中获取WebSocket处理器实例
            if (function_exists('app') && app()->has('websocket.handler')) {
                return app('websocket.handler');
            }
            
            // 尝试从全局变量获取
            if (isset($GLOBALS['websocket_handler'])) {
                return $GLOBALS['websocket_handler'];
            }
            
            // 如果都获取不到，返回null
            return null;
        } catch (\Exception $e) {
            Log::warning('获取WebSocket处理器实例失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 直接通过Redis推送到WebSocket服务器
     * @param string $target 目标类型
     * @param array $testData 测试数据
     * @return array
     */
    private function pushViaDirectRedis($target, $testData)
    {
        try {
            Log::info('尝试通过直接Redis推送: ' . $target);
            
            $redis = \think\facade\Cache::store('redis');
            
            // 构建符合CombinedHandler期望的消息格式
            $message = [
                'event' => 'notification',
                'data' => [
                    'type' => $testData['type'],
                    'title' => $testData['title'],
                    'content' => $testData['content'],
                    'url' => $testData['url'],
                    'icon' => $testData['icon'],
                    'timestamp' => $testData['timestamp'],
                    'test_mode' => true,
                    'test_id' => $testData['test_id']
                ]
            ];
            
            $messageJson = json_encode($message, JSON_UNESCAPED_UNICODE);
            
            // 方法1: 发布到特定频道
            $channelName = $target === 'admin' ? 'admin_notifications' : 'shop_notifications';
            $publishResult = false;
            if (method_exists($redis, 'publish')) {
                $publishResult = $redis->publish($channelName, $messageJson);
                Log::info('Redis发布结果: ' . ($publishResult ? '成功' : '失败') . ', 频道: ' . $channelName);
            }
            
            // 方法2: 存储到特定键，供WebSocket服务器轮询
            $keyName = 'websocket_' . $target . '_notification_' . time();
            $setResult = $redis->set($keyName, $messageJson, 300); // 5分钟过期
            Log::info('Redis存储结果: ' . ($setResult ? '成功' : '失败') . ', 键: ' . $keyName);
            
            // 方法3: 添加到通知队列
            $queueKey = 'websocket_' . $target . '_queue';
            $redis->lpush($queueKey, $messageJson);
            $redis->expire($queueKey, 300); // 5分钟过期
            Log::info('Redis队列推送完成: ' . $queueKey);
            
            $success = $publishResult || $setResult;
            
            return [
                'success' => $success,
                'error' => $success ? null : 'Redis直接推送失败',
                'method' => 'direct_redis',
                'target' => $target,
                'results' => [
                    'publish' => $publishResult,
                    'set' => $setResult,
                    'queue' => true
                ],
                'message_sent' => $message
            ];
            
        } catch (\Exception $e) {
            Log::error('Redis直接推送异常: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Redis直接推送异常: ' . $e->getMessage(),
                'method' => 'direct_redis'
            ];
        }
    }

    /**
     * 根据消息类型构建测试数据
     * @param string $title 原始标题
     * @param string $message 原始消息
     * @param string $type 消息类型
     * @param string $url 原始URL
     * @param int $icon 原始图标
     * @return array
     */
    private function buildTestDataByType($title, $message, $type, $url, $icon)
    {
        // 基础测试数据
        $testData = [
            'title' => $title,
            'content' => $message,
            'type' => $type,
            'url' => $url,
            'icon' => $icon,
            'timestamp' => time(),
            'test_mode' => true,
            'test_id' => uniqid('test_')
        ];

        // 根据消息类型增强内容
        switch ($type) {
            case 'order':
                // 订单相关通知
                if ($title === 'WebSocket测试通知') {
                    $testData['title'] = '订单状态更新通知';
                }
                if ($message === '这是一条WebSocket测试通知') {
                    $testData['content'] = '您有新的订单需要处理，订单号：TEST' . date('YmdHis');
                }
                if (empty($url)) {
                    $testData['url'] = '/admin/order/index';
                }
                if ($icon === 0) {
                    $testData['icon'] = 1; // 成功图标
                }
                // 添加订单相关的额外数据
                $testData['order_data'] = [
                    'order_id' => 'TEST' . date('YmdHis'),
                    'order_status' => 'pending',
                    'amount' => '99.99',
                    'customer' => '测试用户'
                ];
                break;

            case 'chat':
                // 聊天相关通知
                if ($title === 'WebSocket测试通知') {
                    $testData['title'] = '新消息通知';
                }
                if ($message === '这是一条WebSocket测试通知') {
                    $testData['content'] = '您收到了一条新的客服消息，请及时回复';
                }
                if (empty($url)) {
                    $testData['url'] = '/admin/chat/index';
                }
                if ($icon === 0) {
                    $testData['icon'] = 4; // 信息图标
                }
                // 添加聊天相关的额外数据
                $testData['chat_data'] = [
                    'chat_id' => 'CHAT' . date('YmdHis'),
                    'from_user' => '测试用户',
                    'message_type' => 'text',
                    'unread_count' => 1
                ];
                break;

            case 'system':
                // 系统通知
                if ($title === 'WebSocket测试通知') {
                    $testData['title'] = '系统维护通知';
                }
                if ($message === '这是一条WebSocket测试通知') {
                    $testData['content'] = '系统将于今晚进行维护升级，预计耗时30分钟';
                }
                if (empty($url)) {
                    $testData['url'] = '/admin/system/notice';
                }
                if ($icon === 0) {
                    $testData['icon'] = 3; // 警告图标
                }
                // 添加系统相关的额外数据
                $testData['system_data'] = [
                    'notice_id' => 'SYS' . date('YmdHis'),
                    'priority' => 'high',
                    'maintenance_time' => date('Y-m-d H:i:s', time() + 3600),
                    'affected_modules' => ['订单系统', '支付系统']
                ];
                break;

            case 'test_notification':
            default:
                // 默认测试通知，保持原有内容
                if ($icon === 0) {
                    $testData['icon'] = 0; // 默认图标
                }
                // 添加测试相关的额外数据
                $testData['test_data'] = [
                    'test_type' => 'websocket_notification',
                    'test_time' => date('Y-m-d H:i:s'),
                    'test_environment' => config('app.app_debug') ? 'development' : 'production',
                    'test_version' => '1.0.0'
                ];
                break;
        }

        // 记录构建的测试数据
        Log::info('构建的测试数据: ' . json_encode($testData, JSON_UNESCAPED_UNICODE));

        return $testData;
    }


}
