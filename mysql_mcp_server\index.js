const mysql = require('mysql2/promise');
const readline = require('readline');

// 解析命令行参数
const args = process.argv.slice(2);
const params = {};

args.forEach(arg => {
  if (arg.startsWith('--')) {
    const [key, value] = arg.substring(2).split('=');
    params[key] = value;
  }
});

// 数据库连接配置
const dbConfig = {
  host: params.host || '127.0.0.1',
  port: parseInt(params.port || '3306'),
  user: params.user || 'root',
  password: params.password || '',
  database: params.database || 'mysql',
  connectTimeout: 10000,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

// 创建读取标准输入的接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
  terminal: false
});

// 连接池
let pool;

async function initPool() {
  try {
    pool = await mysql.createPool(dbConfig);
    console.error('MySQL MCP Server initialized successfully');
  } catch (error) {
    console.error('Error connecting to MySQL:', error);
    process.exit(1);
  }
}

// 执行SQL查询
async function executeQuery(sql, params = []) {
  try {
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Query timed out after 10000ms')), 10000);
    });
    const [rows] = await Promise.race([
      pool.execute(sql, params),
      timeoutPromise
    ]);
    return rows;
  } catch (error) {
    throw error;
  }
}

// 发送响应
function sendResponse(id, result) {
  const response = {
    jsonrpc: "2.0",
    id,
    result
  };
  process.stdout.write(JSON.stringify(response) + '\n');
}

// 发送错误响应
function sendError(id, code, message) {
  const response = {
    jsonrpc: "2.0",
    id,
    error: {
      code,
      message
    }
  };
  process.stdout.write(JSON.stringify(response) + '\n');
}

// 处理MCP请求
async function handleRequest(request) {
  try {
    const { id, method, params } = JSON.parse(request);
    
    switch (method) {
      case 'initialize':
        sendResponse(id, {
          protocolVersion: "2024-11-05",
          capabilities: {
            tools: {}
          },
          serverInfo: {
            name: "mysql-mcp-server",
            version: "1.0.0"
          }
        });
        break;

      case 'tools/list':
        sendResponse(id, {
          tools: [
            {
              name: "mysql_query",
              description: "Execute a MySQL query",
              inputSchema: {
                type: "object",
                properties: {
                  sql: {
                    type: "string",
                    description: "SQL query to execute"
                  },
                  parameters: {
                    type: "array",
                    description: "Parameters for the SQL query",
                    items: {
                      type: "string"
                    }
                  }
                },
                required: ["sql"]
              }
            }
          ]
        });
        break;

      case 'tools/call':
        if (params.name === 'mysql_query') {
          try {
            const { sql, parameters = [] } = params.arguments;
            const result = await executeQuery(sql, parameters);
            sendResponse(id, {
              content: [
                {
                  type: "text",
                  text: JSON.stringify(result, null, 2)
                }
              ]
            });
          } catch (error) {
            sendError(id, -32000, `Database error: ${error.message}`);
          }
        } else {
          sendError(id, -32601, `Unknown tool: ${params.name}`);
        }
        break;

      default:
        sendError(id, -32601, `Unknown method: ${method}`);
        break;
    }
  } catch (error) {
    console.error('Error handling request:', error);
    sendError('error', -32700, 'Parse error');
  }
}

// 初始化连接池并开始监听请求
initPool().then(() => {
  rl.on('line', handleRequest);
});

// 处理进程退出
process.on('SIGINT', async () => {
  if (pool) {
    await pool.end();
  }
  process.exit(0);
});
