{"mcpServers": {"fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "env": {}, "disabled": true, "autoApprove": []}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "disabled": false, "autoApprove": ["create_entities", "create_relations", "add_observations", "delete_entities", "delete_observations", "delete_relations", "read_graph", "search_nodes", "open_nodes"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "disabled": false, "autoApprove": ["sequential_thinking"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "disabled": false, "autoApprove": ["resolve-library-id", "get-library-docs"]}, "fs-mcp-server": {"command": "npx", "args": ["-y", "@bunas/fs-mcp@latest", "--API_KEY=asdqwezxc"], "disabled": false, "autoApprove": ["read_file_21"]}, "browser-tools-mcp": {"command": "npx", "args": ["@agentdeskai/browser-tools-mcp@latest"], "disabled": false, "autoApprove": ["getConsoleLogs", "getConsoleErrors", "getNetworkErrors", "getNetworkLogs", "takeScreenshot", "getSelectedElement", "wipeLogs", "runAccessibilityAudit", "runPerformanceAudit", "runSEOAudit", "runNextJSAudit", "runDebuggerMode", "runAuditMode", "runBestPracticesAudit"]}, "edgeone-pages-mcp-server": {"command": "npx", "args": ["edgeone-pages-mcp"], "disabled": false, "autoApprove": ["deploy_html", "deploy_folder_or_zip"]}, "knowledge-graph-memory-server": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "disabled": false, "autoApprove": ["create_entities", "create_relations", "add_observations", "delete_entities", "delete_observations", "delete_relations", "read_graph", "search_nodes", "open_nodes"]}, "MySQL Server": {"command": "node", "args": ["mysql_mcp_server/index.js", "--host=rm-bp1y50i48aa95aw2beo.mysql.rds.aliyuncs.com", "--port=3306", "--user=root", "--password=yT636^wQjehF2@dfs", "--database=service_jiaqingf"], "disabled": false, "autoApprove": ["mysql_query"]}, "ssh-mpc-server": {"command": "ssh-mcp-server", "args": ["--host", "***************", "--port", "22", "--username", "root", "--password", "Ks81GHXkAd^U2x@_@"], "disabled": false, "autoApprove": ["execute-command", "upload", "download"]}}}