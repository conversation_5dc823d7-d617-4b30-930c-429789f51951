<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket测试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="number"], select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #45a049;
        }
        button.disconnect {
            background-color: #f44336;
        }
        button.disconnect:hover {
            background-color: #d32f2f;
        }
        button.send {
            background-color: #2196F3;
        }
        button.send:hover {
            background-color: #0b7dda;
        }
        .log-container {
            margin-top: 20px;
            border: 1px solid #ddd;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .log-entry.sent {
            color: #2196F3;
        }
        .log-entry.received {
            color: #4CAF50;
        }
        .log-entry.error {
            color: #f44336;
        }
        .log-entry.info {
            color: #9E9E9E;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            margin-left: 10px;
        }
        .status.connected {
            background-color: #4CAF50;
            color: white;
        }
        .status.disconnected {
            background-color: #f44336;
            color: white;
        }
        .status.connecting {
            background-color: #FFC107;
            color: black;
        }
        .row {
            display: flex;
            margin: 0 -10px;
        }
        .col {
            flex: 1;
            padding: 0 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket测试工具 <span id="status" class="status disconnected">未连接</span></h1>

        <div class="row">
            <div class="col">
                <div class="form-group">
                    <label for="url">WebSocket URL</label>
                    <input type="text" id="url" value="wss://kefu.huohanghang.cn" placeholder="wss://example.com">
                </div>
            </div>
            <div class="col">
                <div class="form-group">
                    <label for="admin_id">管理员ID</label>
                    <input type="number" id="admin_id" value="1" placeholder="管理员ID">
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col">
                <div class="form-group">
                    <label for="nickname">昵称</label>
                    <input type="text" id="nickname" value="管理员" placeholder="昵称">
                </div>
            </div>
            <div class="col">
                <div class="form-group">
                    <label for="token">Token</label>
                    <input type="text" id="token" value="admin_token" placeholder="Token">
                </div>
            </div>
        </div>

        <div class="form-group">
            <button id="connect">连接</button>
            <button id="disconnect" class="disconnect" disabled>断开连接</button>
            <button id="ping" disabled>发送Ping</button>
            <button id="clear-log">清空日志</button>
        </div>

        <div class="row">
            <div class="col">
                <div class="form-group">
                    <label for="event">事件名称</label>
                    <input type="text" id="event" value="admin_notification" placeholder="事件名称">
                </div>
            </div>
            <div class="col">
                <div class="form-group">
                    <label for="message-type">消息类型</label>
                    <select id="message-type">
                        <option value="system">系统通知</option>
                        <option value="personal">个人通知</option>
                        <option value="admin_notification">默认通知</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label for="message">消息内容</label>
            <input type="text" id="message" value="这是一条测试通知" placeholder="消息内容">
        </div>

        <div class="form-group">
            <button id="send" class="send" disabled>发送消息</button>
        </div>

        <div class="log-container" id="log"></div>
    </div>

    <script>
        let socket = null;

        // DOM元素
        const connectBtn = document.getElementById('connect');
        const disconnectBtn = document.getElementById('disconnect');
        const pingBtn = document.getElementById('ping');
        const sendBtn = document.getElementById('send');
        const clearLogBtn = document.getElementById('clear-log');
        const statusEl = document.getElementById('status');
        const logEl = document.getElementById('log');

        // 连接WebSocket
        connectBtn.addEventListener('click', () => {
            if (socket) {
                addLog('已有连接，请先断开', 'error');
                return;
            }

            const url = document.getElementById('url').value;
            const adminId = document.getElementById('admin_id').value;
            const nickname = document.getElementById('nickname').value;
            const token = document.getElementById('token').value;

            if (!url) {
                addLog('请输入WebSocket URL', 'error');
                return;
            }

            if (!adminId) {
                addLog('请输入管理员ID', 'error');
                return;
            }

            // 构建URL - 确保URL格式正确
            let baseUrl = url;
            // 如果URL不包含端口号，添加默认端口20211
            if (baseUrl.indexOf(':') === -1 || baseUrl.indexOf(':', 6) === -1) {
                // 检查URL是否以/结尾
                if (baseUrl.endsWith('/')) {
                    baseUrl = baseUrl.slice(0, -1);
                }
            }

            const fullUrl = `${baseUrl}?admin_id=${adminId}&nickname=${encodeURIComponent(nickname)}&token=${encodeURIComponent(token)}&type=admin&t=${Date.now()}`;

            addLog(`完整连接URL: ${fullUrl}`, 'info');

            addLog(`正在连接: ${fullUrl}`, 'info');
            statusEl.textContent = '连接中...';
            statusEl.className = 'status connecting';

            try {
                socket = new WebSocket(fullUrl);

                socket.onopen = (event) => {
                    addLog('连接已建立', 'info');
                    statusEl.textContent = '已连接';
                    statusEl.className = 'status connected';

                    // 启用按钮
                    disconnectBtn.disabled = false;
                    pingBtn.disabled = false;
                    sendBtn.disabled = false;
                    connectBtn.disabled = true;
                };

                socket.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        addLog(`收到消息: ${JSON.stringify(data, null, 2)}`, 'received');
                    } catch (e) {
                        addLog(`收到消息(非JSON): ${event.data}`, 'received');
                    }
                };

                socket.onclose = (event) => {
                    addLog(`连接已关闭: 代码=${event.code}, 原因=${event.reason || 'N/A'}`, 'info');
                    statusEl.textContent = '未连接';
                    statusEl.className = 'status disconnected';

                    // 禁用按钮
                    disconnectBtn.disabled = true;
                    pingBtn.disabled = true;
                    sendBtn.disabled = true;
                    connectBtn.disabled = false;

                    socket = null;
                };

                socket.onerror = (error) => {
                    addLog(`连接错误: ${error.message || 'Unknown error'}`, 'error');
                };
            } catch (e) {
                addLog(`创建WebSocket对象失败: ${e.message}`, 'error');
                statusEl.textContent = '未连接';
                statusEl.className = 'status disconnected';
            }
        });

        // 断开连接
        disconnectBtn.addEventListener('click', () => {
            if (!socket) {
                addLog('没有活动的连接', 'error');
                return;
            }

            socket.close();
            addLog('正在关闭连接...', 'info');
        });

        // 发送Ping
        pingBtn.addEventListener('click', () => {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                addLog('WebSocket未连接', 'error');
                return;
            }

            const pingData = {
                event: 'ping',
                data: {
                    timestamp: Date.now()
                }
            };

            socket.send(JSON.stringify(pingData));
            addLog(`发送Ping: ${JSON.stringify(pingData)}`, 'sent');
        });

        // 发送消息
        sendBtn.addEventListener('click', () => {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                addLog('WebSocket未连接', 'error');
                return;
            }

            const event = document.getElementById('event').value;
            const messageType = document.getElementById('message-type').value;
            const message = document.getElementById('message').value;
            const adminId = document.getElementById('admin_id').value;

            if (!event) {
                addLog('请输入事件名称', 'error');
                return;
            }

            if (!message) {
                addLog('请输入消息内容', 'error');
                return;
            }

            const data = {
                event: event,
                data: {
                    type: messageType,
                    title: '测试通知',
                    content: message,
                    admin_id: adminId,
                    timestamp: Date.now()
                }
            };

            socket.send(JSON.stringify(data));
            addLog(`发送消息: ${JSON.stringify(data, null, 2)}`, 'sent');
        });

        // 清空日志
        clearLogBtn.addEventListener('click', () => {
            logEl.innerHTML = '';
        });

        // 添加日志
        function addLog(message, type) {
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;

            const timestamp = new Date().toLocaleTimeString();
            logEntry.textContent = `[${timestamp}] ${message}`;

            logEl.appendChild(logEntry);
            logEl.scrollTop = logEl.scrollHeight;
        }
    </script>
</body>
</html>
