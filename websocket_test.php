<?php
/**
 * WebSocket连接测试脚本
 */

// 使用ReactPHP的WebSocket客户端进行测试
require_once 'vendor/autoload.php';

use Ratchet\Client\WebSocket;
use Ratchet\Client\Connector;

echo "开始WebSocket连接测试...\n";

$connector = new Connector();

// 构建WebSocket URL
$wsUrl = 'ws://kefu.huohanghang.cn?admin_id=1&nickname=测试管理员&token=test_token&type=admin&client=5';

echo "连接URL: {$wsUrl}\n";

$connector($wsUrl)
    ->then(function (WebSocket $conn) {
        echo "WebSocket连接成功!\n";
        
        // 发送ping消息
        $pingData = json_encode([
            'event' => 'ping',
            'data' => [
                'timestamp' => time()
            ]
        ]);
        
        echo "发送ping消息: {$pingData}\n";
        $conn->send($pingData);
        
        // 发送测试通知
        $notificationData = json_encode([
            'event' => 'admin_notification',
            'data' => [
                'type' => 'system_notification',
                'title' => 'WebSocket测试通知',
                'content' => '这是通过WebSocket发送的测试通知',
                'url' => '',
                'icon' => 1,
                'timestamp' => time()
            ]
        ]);
        
        echo "发送通知消息: {$notificationData}\n";
        $conn->send($notificationData);
        
        // 监听消息
        $conn->on('message', function ($msg) {
            echo "收到消息: {$msg}\n";
        });
        
        // 5秒后关闭连接
        \React\EventLoop\Loop::get()->addTimer(5, function () use ($conn) {
            echo "关闭WebSocket连接\n";
            $conn->close();
        });
        
    }, function (\Exception $e) {
        echo "WebSocket连接失败: {$e->getMessage()}\n";
    });

// 运行事件循环
\React\EventLoop\Loop::get()->run();

echo "WebSocket测试完成\n";