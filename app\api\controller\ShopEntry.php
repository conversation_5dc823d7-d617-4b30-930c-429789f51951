<?php
namespace app\api\controller;

use app\common\basics\Api;
use app\common\server\JsonServer;
use app\api\logic\ShopEntryLogic;
use app\api\logic\PayLogic;
use app\api\validate\ShopEntryValidate;
use app\common\logic\ShopTierLogic;
use app\common\enum\ClientEnum;
use app\common\enum\PayEnum;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\Log;

use function GuzzleHttp\json_decode;

/**
 * 商家入驻新接口
 * Class ShopEntry
 * @package app\api\controller
 */
class ShopEntry extends Api
{
    public $like_not_need_login = ['getEntryOptions'];

    /**
     * 获取入驻选项配置
     * @return \think\response\Json
     */
    public function getEntryOptions()
    {
        $type= $this->request->get('type/d')??0;
      
        $result = ShopEntryLogic::getEntryOptions($type);
        return JsonServer::success('获取成功', $result);
    }

    /**
     * 0元入驻申请
     * @return \think\response\Json
     */
    public function freeEntry()
    {
         try {
             $post = $this->request->post();
             validate(ShopEntryValidate::class)->scene('freeEntry')->check($post);
         } catch (ValidateException $e) {
             return JsonServer::error($e->getError());
         }
 
         $result = ShopEntryLogic::freeEntry($this->user_id, $post);
         if ($result === false) {
             return JsonServer::error(ShopEntryLogic::getError() ?: '0元入驻申请失败');
         }
 
         // 发送管理员通知
         try {
             // 获取用户信息
             $user = \app\common\model\user\User::find($this->user_id);
             $userInfo = $user ? ($user->nickname ?: $user->mobile) : '未知用户';

             // 使用与AdminNotificationTrigger相同的方式发送通知
             $this->sendAdminNotificationViaWebSocket(
                 '🏪 新的0元入驻申请',
                 "用户：{$userInfo}\n申请时间：" . date('Y-m-d H:i:s') . "\n请及时审核处理。",
                 'info_notification',
                 '/admin/shop_entry/list'
             );
         } catch (\Exception $e) {
             // 通知发送失败不影响主流程，只记录日志
             Log::error('0元入驻通知发送失败: ' . $e->getMessage());
         }

        return JsonServer::success('入驻成功', $result);
    }

    /**
     * 商家会员支付
     * @return \think\response\Json
     */
    public function memberPay()
    {
        try {
            $post = $this->request->post();
            validate(ShopEntryValidate::class)->scene('pay')->check($post);
        } catch (ValidateException $e) {
            return JsonServer::error($e->getError());
        }

        $result = ShopEntryLogic::createMemberPayOrder($this->user_id, $post);
        if ($result === false) {
            return JsonServer::error(ShopEntryLogic::getError() ?: '创建支付订单失败');
        }

        // 调用统一支付接口
        $post['from'] = 'ruzhucharge';
        $post['order_id'] = $result['order_sn'];
        return $this->unifiedPay($post);
    }

    /**
     * 实力厂商支付
     * @return \think\response\Json
     */
    public function premiumPay()
    {
        try {
            $post = $this->request->post();
            validate(ShopEntryValidate::class)->scene('pay')->check($post);
        } catch (ValidateException $e) {
            return JsonServer::error($e->getError());
        }

        $result = ShopEntryLogic::createPremiumPayOrder($this->user_id, $post);
        if ($result === false) {
            return JsonServer::error(ShopEntryLogic::getError() ?: '创建支付订单失败');
        }

        // 调用统一支付接口
        $post['from'] = 'ruzhucharge';
        $post['order_id'] = $result['order_sn'];
        return $this->unifiedPay($post);
    }

    /**
     * 商家会员入驻信息填写
     * @return \think\response\Json
     */
    public function memberApply()
    {
        try {
            $post = $this->request->post();
            validate(ShopEntryValidate::class)->scene('apply')->check($post);
        } catch (ValidateException $e) {
            return JsonServer::error($e->getError());
        }

        $result = ShopEntryLogic::memberApply($this->user_id, $post);
        if ($result === false) {
            return JsonServer::error(ShopEntryLogic::getError() ?: '入驻申请失败');
        }
        return JsonServer::success('入驻成功', $result);
    }

    /**
     * 实力厂商入驻信息填写
     * @return \think\response\Json
     */
    public function premiumApply()
    {
        try {
            $post = $this->request->post();
            validate(ShopEntryValidate::class)->scene('apply')->check($post);
        } catch (ValidateException $e) {
            return JsonServer::error($e->getError());
        }

        $result = ShopEntryLogic::premiumApply($this->user_id, $post);
        if ($result === false) {
            return JsonServer::error(ShopEntryLogic::getError() ?: '入驻申请失败');
        }
        return JsonServer::success('入驻成功', $result);
    }

    /**
     * 记录用户点击0元入驻按钮
     * @return \think\response\Json
     */
    public function clickFreeEntry()
    {
        $result = ShopEntryLogic::recordFreeEntryClick($this->user_id);
        if ($result === false) {
            return JsonServer::error(ShopEntryLogic::getError() ?: '记录失败');
        }
        return JsonServer::success('记录成功');
    }

    /**
     * 获取用户入驻状态
     * @return \think\response\Json
     */
    public function getEntryStatus()
    {   
        $get = $this->request->get();
        $result = ShopEntryLogic::getEntryStatus($this->user_id, $get);
        return JsonServer::success('获取成功', $result);
    }

    /**
     * 获取可升级的商家等级列表
     * @return \think\response\Json
     */
    public function getUpgradableTiers()
    {
        // 检查用户是否已经是商家
        $user = \app\common\model\user\User::find($this->user_id);
        if (!$user || !$user->shop_id) {
            return JsonServer::error('您还不是商家，无法升级等级');
        }

        $result = ShopTierLogic::getUpgradableTiers($user->shop_id);
        return JsonServer::success('获取成功', $result);
    }

    /**
     * 创建商家等级升级订单
     * @return \think\response\Json
     */
    public function createTierUpgradeOrder()
    {
        try {
            $post = $this->request->post();
            validate(ShopEntryValidate::class)->scene('upgrade')->check($post);

            $targetTier = intval($post['target_tier']);

            // 检查用户是否已经是商家
            $user = \app\common\model\user\User::find($this->user_id);
            if (!$user || !$user->shop_id) {
                return JsonServer::error('您还不是商家，无法升级等级');
            }

            // 获取当前商家信息
            $shop = \app\common\model\shop\Shop::find($user->shop_id);
            if (!$shop) {
                return JsonServer::error('商家信息不存在');
            }

            if ($shop->tier_level >= $targetTier) {
                return JsonServer::error('目标等级不能低于或等于当前等级');
            }

            // 使用ShopTierLogic创建升级订单
            $result = ShopTierLogic::createUpgradeOrder($user->shop_id, $targetTier, $this->user_id);
            if ($result === false) {
                return JsonServer::error(ShopTierLogic::getError() ?: '创建升级订单失败');
            }

            return JsonServer::success('升级订单创建成功', $result);
        } catch (\Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * 发起商家等级升级支付
     * @return \think\response\Json
     */
    public function payTierUpgrade()
    {
        try {
            $post = $this->request->post();
            validate(ShopEntryValidate::class)->scene('upgradePay')->check($post);

            $orderSn = $post['order_sn'];

            // 查找订单
            $order = \app\common\model\shop\ShopMerchantfees::where('order_sn', $orderSn)->find();
            if (!$order) {
                return JsonServer::error('订单不存在');
            }

            if ($order->status != 0) {
                return JsonServer::error('订单状态异常');
            }

            // 验证订单是否属于当前用户
            if ($order->user_id != $this->user_id) {
                return JsonServer::error('无权操作此订单');
            }

            // 验证是否为升级订单
            if ($order->tier_type != \app\common\enum\ShopTierEnum::TYPE_UPGRADE) {
                return JsonServer::error('订单类型错误');
            }

            // 调用统一支付接口
            $post['from'] = 'ruzhucharge';
            $post['order_id'] = $order->order_sn;
            return $this->unifiedPay($post);
        } catch (\think\exception\ValidateException $e) {
            return JsonServer::error($e->getError());
        } catch (\Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /*
    * 广告位支付
    */
    public function payAdSlot()
    {
        $params = $this->request->post();

        // --- 开始修改/添加支付逻辑 ---
        try {
            // 验证必要参数
            if (empty($params['ad_order_id'])) {
                return JsonServer::error('缺少广告订单ID (ad_order_id)');
            }
            if (empty($params['pay_way'])) {
                return JsonServer::error('请选择支付方式 (pay_way)');
            }

            // 获取广告订单，并验证归属权和状态
            $adOrder = Db::name('ad_order')->where('id', $params['ad_order_id'])
                ->find();
            if (!$adOrder) {
                return JsonServer::error('广告订单不存在或无权访问');
            }

            // 检查订单是否已支付或处于不可支付状态 (假设 pay_status = 0 表示未支付)
            if ($adOrder['status'] != 0) {
                return JsonServer::error('此广告订单状态无法支付');
            }

            // 调用统一支付接口
            $params['from'] = 'AdOrder';
            $params['order_id'] = $adOrder['id'];
            $params['client'] = 1; // 客户端类型: 商家API
            return $this->unifiedPay($params);
        } catch (\Exception $e) {
            return JsonServer::error('支付请求处理异常，请稍后重试');
        }
    }

    /**
     * 统一支付接口
     * @param array $post
     * @return \think\response\Json
     */
    public function unifiedPay($post = [])
    {
        if (empty($post)) {
            $post = $this->request->post();
        }

        $post['pay_way']=1;

        if (!isset($post['from']) || empty($post['from'])) {
            return JsonServer::error('缺少来源参数 from');
        }

        if (!isset($post['order_id']) || empty($post['order_id'])) {
            return JsonServer::error('缺少订单编号');
        }

        $pay_way = $post['pay_way'];
        $from = $post['from'];
        $client = $post['client'] ?? $this->client ?? ClientEnum::h5;
        $order_id = $post['order_id'];

        // 根据来源类型动态设置参数
        $user_id = $this->user_id;
        $shop_id = null;
        if (isset($user_id) && $user_id) {
            $user = \app\common\model\user\User::find($user_id);
            $shop_id = $user ? $user->shop_id : null;
            $admin_id =  $shop_id ? Db::name('shop_admin')->where('account',$user->mobile)->value('id') : null;
        }

        // 验证订单归属和状态
        switch ($from) {
            case 'bondcharge':
                if ($shop_id) {
                    $order = \app\common\model\shop\ShopDeposit::where('id', $order_id)
                        ->where('shop_id', $shop_id)
                        ->find();
                    if (!$order) {
                        return JsonServer::error('保证金订单不存在或无权访问');
                    }
                    if ($order->status != 0) {
                        return JsonServer::error('保证金订单状态异常');
                    }
                    $post['shop_id'] = $shop_id??0;
                    $post['admin_id'] = $admin_id??0;
                } else {
                    return JsonServer::error('无法获取商家信息，无法处理保证金订单');
                }
                break;
            case 'replenishDeposit':
                if ($shop_id) {
                    $order = \app\common\model\shop\ShopDeposit::where('order_sn', $order_id)
                        ->where('shop_id', $shop_id)
                        ->find();
                    if (!$order) {
                        return JsonServer::error('补缴保证金订单不存在或无权访问');
                    }
                    if ($order->status != 0) {
                        return JsonServer::error('补缴保证金订单状态异常');
                    }
                    $post['shop_id'] = $shop_id;
                   
                } else {
                    return JsonServer::error('无法获取商家信息，无法处理补缴保证金订单');
                }
                break;
            case 'ruzhucharge':
                $order = \app\common\model\shop\ShopMerchantfees::where('order_sn', $order_id)
                    ->where('user_id', $user_id)
                    ->find();
                if (!$order) {
                    return JsonServer::error('入驻订单不存在或无权访问');
                }
                if ($order->status != 0) {
                    return JsonServer::error('入驻订单状态异常');
                }
                $post['shop_id'] = $shop_id;
                break;
            case 'AdOrder':
                $order = Db::name('ad_order')->where('id', $order_id)->find();
                if (!$order) {
                    return JsonServer::error('广告订单不存在或无权访问');
                }
                if ($order['status'] != 0) {
                    return JsonServer::error('广告订单状态异常');
                }
                if ($shop_id && $order['shop_id'] != $shop_id) {
                    return JsonServer::error('广告订单无权访问');
                }
                $post['shop_id'] = $shop_id;
                break;
            default:
                // 其他类型的订单可以添加额外的验证逻辑
                break;
        }

        try {
            switch ($pay_way) {
                case PayEnum::WECHAT_PAY:
                 

                    $pay_info = PayLogic::wechatPay($order_id, $from, $client, $post);
                    break;
                case PayEnum::ALI_PAY:
                    $pay_info = PayLogic::aliPay($order_id, $from, $client, $post);
                    break;
                default:
                    return JsonServer::error('不支持的支付方式');
            }

            // 检查支付结果
            if ($pay_info === false) {
                return JsonServer::error(PayLogic::getError() ?: '创建支付订单失败');
            }

            // 如果返回的是Json响应对象，直接返回
            if ($pay_info instanceof \think\response\Json) {
                return $pay_info;
            }

            // 如果是数组，转换为标准响应
            if (is_array($pay_info)) {
                return JsonServer::success('支付订单创建成功', $pay_info);
            }

            // 如果是字符串（支付宝返回的HTML），包装返回
            if (is_string($pay_info)) {
                return JsonServer::success('支付订单创建成功', ['pay_data' => $pay_info]);
            }

            // 其他情况，尝试转换为数组
            return JsonServer::success('支付订单创建成功', (array)$pay_info);
        } catch (\Exception $e) {
            Log::error('Payment processing error: ' . $e->getMessage());
            return JsonServer::error('支付处理异常：' . $e->getMessage());
        }
    }









    /**
     * 通过WebSocket发送管理员通知（模拟AdminNotificationTrigger）
     * @param string $title 通知标题
     * @param string $content 通知内容
     * @param string $type 通知类型
     * @param string $url 跳转链接
     * @return bool
     */
    private function sendAdminNotificationViaWebSocket($title, $content, $type = 'info_notification', $url = '')
    {
        try {
            // 构建与AdminNotificationTrigger相同的消息格式
            $message = [
                'event' => 'admin_notification',
                'data' => [
                    'type' => $type,
                    'title' => $title,
                    'content' => $content,
                    'url' => $url,
                    'icon' => $this->getNotificationIcon($type),
                    'timestamp' => time() * 1000 // 毫秒时间戳
                ]
            ];

            // 通过Redis发布消息（与AdminNotificationTrigger的WebSocket服务器通信）
            $redis = \think\facade\Cache::store('redis');
            $result = $redis->publish('admin_notifications', json_encode($message));

            if ($result > 0) {
                Log::info('0元入驻通知发送成功，订阅者数量: ' . $result);
                return true;
            } else {
                Log::warning('0元入驻通知发送失败，没有订阅者');
                return false;
            }
        } catch (\Exception $e) {
            Log::error('WebSocket通知发送失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 根据通知类型获取图标
     * @param string $type
     * @return int
     */
    private function getNotificationIcon($type)
    {
        $icons = [
            'system_notification' => 1,
            'error_notification' => 2,
            'warning_notification' => 3,
            'info_notification' => 4,
            'success_notification' => 1
        ];

        return $icons[$type] ?? 0;
    }

    /**
     * 发送HTTP请求
     * @param string $url
     * @param array $params
     * @return string
     */
    private function sendHttpRequest($url, $params = [])
    {
        // 构建查询字符串
        $queryString = http_build_query($params);
        $fullUrl = $url . '?' . $queryString;
        
        // 使用cURL发送请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $fullUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'ShopEntry/1.0');
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new \Exception("HTTP请求失败: {$error}");
        }
        
        if ($httpCode !== 200) {
            throw new \Exception("HTTP请求返回错误状态码: {$httpCode}");
        }
        
        return $response;
    }

    /**
     * 处理支付（旧方法，保留以备兼容）
     * @param array $order
     * @param array $post
     * @return \think\response\Json
     */
    private function processPayment($order, $post)
    {
        $pay_way = $post['pay_way'] ?? PayEnum::WECHAT_PAY;
        $from = $post['from'] ?? $this->client ?? ClientEnum::h5;

        try {
            switch ($pay_way) {
                case PayEnum::WECHAT_PAY:
                    $pay_info = PayLogic::wechatPay($order['order_sn'], 'ruzhucharge', $from, $post);
                    break;
                case PayEnum::ALI_PAY:
                    $pay_info = PayLogic::aliPay($order['order_sn'], 'ruzhucharge', $from, $post);
                    break;
                default:
                    return JsonServer::error('不支持的支付方式');
            }

            // 检查支付结果
            if ($pay_info === false) {
                return JsonServer::error(PayLogic::getError() ?: '创建支付订单失败');
            }

            // 如果返回的是Json响应对象，直接返回
            if ($pay_info instanceof \think\response\Json) {
                return $pay_info;
            }

            // 如果是数组，转换为标准响应
            if (is_array($pay_info)) {
                return JsonServer::success('支付订单创建成功', $pay_info);
            }

            // 如果是字符串（支付宝返回的HTML），包装返回
            if (is_string($pay_info)) {
                return JsonServer::success('支付订单创建成功', ['pay_data' => $pay_info]);
            }

            // 其他情况，尝试转换为数组
            return JsonServer::success('支付订单创建成功', (array)$pay_info);
        } catch (\Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }
}
