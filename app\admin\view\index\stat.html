{layout name="layout1" /}
<link rel="stylesheet" href="/static/plug/layui-admin/dist/layuiadmin/layui/css/layui.css?v={$front_version}" media="all">
<link rel="stylesheet" href="/static/plug/layui-admin/dist/layuiadmin/style/admin.css?v={$front_version}" media="all">
<link rel="stylesheet" href="/static/plug/layui-admin/dist/layuiadmin/style/like.css?v={$front_version}" media="all">
<!-- FontAwesome 图标库 - 远程CDN -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<script src="__PUBLIC__/static/lib/layui/layui.js"></script>
<script src="__PUBLIC__/static/admin/js/app.js"></script>
<style>
    /* 导入现代字体 - 使用Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

    /* 全局CSS变量定义 */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --danger-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --info-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        --purple-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

        --glass-bg: rgba(255, 255, 255, 0.1);
        --glass-border: rgba(255, 255, 255, 0.2);
        --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

        --neon-blue: #00d4ff;
        --neon-purple: #b537f2;
        --neon-pink: #ff006e;
        --neon-green: #39ff14;

        --animation-speed: 0.3s;
        --hover-scale: 1.05;
        --card-radius: 20px;
        --shadow-light: 0 4px 20px rgba(0, 0, 0, 0.1);
        --shadow-heavy: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    /* 基础样式 */
    .header-font{
        font-size: 28px;
        font-weight: 800;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        letter-spacing: -0.5px;
        position: relative;
        display: inline-block;
    }

    .header-font::after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 0;
        width: 60px;
        height: 3px;
        background: var(--primary-gradient);
        border-radius: 2px;
        animation: glow 2s ease-in-out infinite alternate;
    }

    @keyframes glow {
        from { box-shadow: 0 0 5px var(--neon-blue); }
        to { box-shadow: 0 0 20px var(--neon-blue), 0 0 30px var(--neon-blue); }
    }

    .header-time{
        margin-left: 20px;
        color: var(--layui-text-color-secondary, #6b7280);
        font-size: 14px;
        font-weight: 500;
        padding: 8px 16px;
        background: rgba(103, 126, 234, 0.1);
        border-radius: 20px;
        border: 1px solid rgba(103, 126, 234, 0.2);
        backdrop-filter: blur(10px);
    }

    /* 修复布局问题 - 确保内容不被遮挡 */
    .wrapper {
        max-width: none;
        width: 100%;
        padding: 20px;
        box-sizing: border-box;
        margin: 0;
        overflow-x: visible;
        position: relative;
    }

    .wrapper::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
        animation: backgroundShift 20s ease-in-out infinite;
    }

    @keyframes backgroundShift {
        0%, 100% { opacity: 0.3; }
        50% { opacity: 0.6; }
    }

    /* 确保页面内容适应iframe容器 */
    body {
        margin: 0;
        padding: 0;
        width: 100%;
        min-height: 100vh;
        overflow-x: hidden;
        background: #f8fafc;
        color: var(--layui-text-color, #333);
        transition: all 0.3s ease;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        position: relative;
    }

    /* 确保所有容器都不会超出父容器宽度 */
    * {
        box-sizing: border-box;
    }

    .layui-row {
        width: 100%;
        max-width: 100%;
    }

    .layui-col-sm3,
    .modern-stat-card {
        max-width: 100%;
    }

    /* 确保行布局正确 */
    .layui-row {
        margin: 0 -7.5px;
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        max-width: 100%;
    }

    /* 调整列布局 */
    .layui-col-sm3 {
        width: 25%;
        padding: 0 7.5px;
        box-sizing: border-box;
        margin-bottom: 15px;
    }

    /* 统计卡片网格布局 */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 24px;
        margin-bottom: 40px;
    }

    /* 现代统计卡片布局调整 */
    .modern-stat-card {
        background: #ffffff;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        padding: 30px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        position: relative;
        overflow: hidden;
        border: 1px solid #e2e8f0;
    }

    .modern-stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.5s;
    }

    .modern-stat-card:hover::before {
        left: 100%;
    }

    .modern-stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
        border-color: #cbd5e0;
    }

    .modern-stat-card::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(103, 126, 234, 0.1) 50%, transparent 70%);
        opacity: 0;
        transition: opacity var(--animation-speed);
        pointer-events: none;
    }

    .modern-stat-card:hover::after {
        opacity: 1;
    }

    /* 统计卡片图标 */
    .stat-icon {
        width: 70px;
        height: 70px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        font-size: 32px;
        color: #fff;
        flex-shrink: 0;
        position: relative;
        overflow: hidden;
        transition: all var(--animation-speed) ease;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        z-index: 1;
    }

    .stat-icon::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: inherit;
        filter: blur(20px);
        opacity: 0.7;
        z-index: -1;
        transform: scale(1.2);
    }

    .stat-icon::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: all 0.6s ease;
        z-index: -1;
    }

    .modern-stat-card:hover .stat-icon::after {
        width: 100px;
        height: 100px;
    }

    .modern-stat-card:hover .stat-icon {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
    }

    /* 更新渐变色调，使其更现代、柔和 */
    .stat-icon.primary { background: linear-gradient(135deg, #5E72E4 0%, #825EE4 100%); }
    .stat-icon.success { background: linear-gradient(135deg, #2DCE89 0%, #2DCEB3 100%); }
    .stat-icon.warning { background: linear-gradient(135deg, #FB6340 0%, #FBB140 100%); }
    .stat-icon.danger  { background: linear-gradient(135deg, #F5365C 0%, #F56036 100%); }
    .stat-icon.info    { background: linear-gradient(135deg, #11CDEF 0%, #1171EF 100%); }
    /* 为可能的第六个卡片（如“待发货订单”）添加一个新渐变 */
    .stat-icon.special { background: linear-gradient(135deg, #FF0080 0%, #FF8C00 100%); } /* 示例：洋红到橙色 */
    .stat-icon.purple  { background: linear-gradient(135deg, #8A2BE2 0%, #DA70D6 100%); } /* 示例：紫罗兰到兰花紫 */


    /* 统计卡片标题 */
    .stat-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px; /* 调整间距 */
    }

    .stat-title {
        font-size: 16px; /* 调整标题字号 */
        font-weight: 500; /* 调整字重 */
        color: var(--layui-text-color-secondary, #4b5563); /* 使用更柔和的次要文字颜色 */
        margin: 0;
    }

    .stat-badge {
        background-color: var(--layui-badge-bg-color, #e0f2fe); /* 调整徽章背景色 */
        color: var(--layui-badge-text-color, #0284c7);
        padding: 4px 10px; /* 微调内边距 */
        border-radius: 12px; /* 调整圆角 */
        font-size: 12px;
        font-weight: 600; /* 加强字重 */
    }

    /* 统计数值 */
    .stat-value {
        font-size: 32px; /* 调整数值字号 */
        font-weight: 700;
        color: var(--layui-text-color-emphasis, #111827); /* 使用更深的强调色 */
        margin-bottom: 8px; /* 调整间距 */
        line-height: 1.1;
    }

    /* 变化指示器 */
    .stat-change {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px; /* 调整间距 */
    }

    .yesterday-label {
        font-size: 13px; /* 调整字号 */
        color: var(--layui-text-color-placeholder, #9ca3af); /* 使用占位符颜色 */
    }

    .change-indicator {
        display: flex;
        align-items: center;
        gap: 5px; /* 调整图标与文字间距 */
        font-size: 13px;
        font-weight: 600; /* 加强字重 */
        padding: 4px 8px; /* 调整内边距 */
        border-radius: 6px; /* 调整圆角 */
    }

    .change-up {
        color: var(--layui-color-danger, #dc2626);
        background-color: var(--layui-color-danger-light, #fef2f2);
    }

    .change-down {
        color: var(--layui-color-success, #16a34a);
        background-color: var(--layui-color-success-light, #f0fdf4);
    }

    /* 统计卡片底部 */
    .stat-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 12px; /* 调整上边距 */
        border-top: 1px solid var(--layui-border-color-extra-light, #f3f4f6); /* 使用更浅的分割线 */
        font-size: 13px;
        color: var(--layui-text-color-placeholder, #9ca3af);
    }

    .stat-total {
        font-weight: 600;
        color: var(--layui-text-color-secondary, #4b5563);
    }

    /* 响应式布局 */
    @media (max-width: 1200px) {
        .layui-col-sm3,
        .modern-stat-card {
            width: 33.333%;
        }
    }

    @media (max-width: 768px) {
        .layui-col-sm3,
        .modern-stat-card {
            width: 50%;
        }

        .wrapper {
            padding: 20px;
        }
    }

    @media (max-width: 480px) {
        .layui-col-sm3,
        .modern-stat-card {
            width: 100%;
        }
    }

    /* 修复表格和图表容器的宽度 */
    .modern-table-container,
    .modern-chart-container {
        max-width: 100%;
        overflow-x: auto;
    }

    /* 强制限制所有内容的最大宽度 */
    .wrapper > * {
        max-width: 100%;
        overflow-x: auto;
    }

    /* 确保表格容器不会超出视口 */
    .layui-table-view {
        max-width: 100% !important;
        overflow-x: auto !important;
    }

    /* 确保所有表格都能正确显示 */
    table {
        width: 100% !important;
        table-layout: auto !important;
    }

    /* 特别处理商家排行榜的网格布局 */
    div[style*="grid-template-columns"] {
        max-width: 100% !important;
        overflow-x: auto !important;
        grid-template-columns: 1fr 1fr !important;
        gap: 16px !important;
    }

    @media (max-width: 768px) {
        div[style*="grid-template-columns"] {
            grid-template-columns: 1fr !important;
        }
    }

    /* 快捷操作样式 */
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 30px;
        margin: 40px 0;
    }

    .quick-action-card {
        background-color: var(--layui-card-bg-color, #fff);
        border-radius: 16px; /* 增大圆角 */
        padding: 24px;
        text-decoration: none;
        color: var(--layui-text-color, inherit);
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border: 1px solid var(--layui-border-color, #e6e6e6);
        display: block;
    }

    .quick-action-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.12);
        text-decoration: none;
        color: var(--layui-text-color, inherit);
    }

    .quick-action-icon {
        width: 64px; /* 增大图标容器 */
        height: 64px;
        border-radius: 20px; /* 增大圆角 */
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16px;
        font-size: 32px; /* 增大图标字号 */
        color: #fff;
    }

    .quick-action-icon.primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    .quick-action-icon.success { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
    .quick-action-icon.info { background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); }
    .quick-action-icon.warning { background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%); }

    .quick-action-title {
        font-size: 19px; /* 增大标题字号 */
        font-weight: 600;
        color: var(--layui-text-color-strong, #333);
        margin: 0 0 8px 0;
    }

    .quick-action-desc {
        font-size: 14px;
        color: var(--layui-text-color-secondary, #666);
        margin: 0;
        line-height: 1.6; /* 调整行高 */
    }

    /* 表格容器样式 */
    .modern-table-container {
        background-color: var(--layui-card-bg-color, #fff);
        border-radius: 16px; /* 增大圆角 */
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        overflow: hidden;
        margin-bottom: 24px;
        border: 1px solid var(--layui-border-color, #e6e6e6);
    }

    .table-header {
        padding: 20px 24px;
        background-color: var(--layui-header-bg-color, linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%));
        border-bottom: 1px solid var(--layui-border-color, #e2e8f0);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .table-title {
        font-size: 19px;
        font-weight: 600;
        color: var(--layui-text-color-strong, #1e293b);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .table-title i {
        color: #3b82f6;
    }

    .table-content {
        padding: 0;
    }

    /* 移除表格拖动框和调整功能 */
    .modern-table-container .layui-table-view {
        border: none !important;
        overflow: hidden !important;
    }

    .modern-table-container .layui-table-header {
        border: none !important;
    }

    .modern-table-container .layui-table-body {
        border: none !important;
        overflow: visible !important;
    }

    /* 移除列拖动功能 */
    .modern-table-container .layui-table th {
        resize: none !important;
        user-select: none !important;
        cursor: default !important;
    }

    /* 移除表格边框和拖动条 */
    .modern-table-container .layui-table,
    .modern-table-container .layui-table tr,
    .modern-table-container .layui-table td,
    .modern-table-container .layui-table th {
        border: 1px solid #f0f0f0 !important;
        border-collapse: collapse !important;
    }

    /* 调整表格行高 */
    .modern-table-container .layui-table td,
    .modern-table-container .layui-table th {
        height: 65px !important; /* 增加行高以容纳图片 */
        line-height: 1.4 !important;
        padding: 12px 15px !important; /* 调整内边距 */
        vertical-align: middle !important;
    }

    /* 优化商家信息显示 */
    .modern-table-container .layui-table td {
        overflow: visible !important;
        text-overflow: unset !important;
        white-space: normal !important;
    }

    /* 商家信息行样式优化 */
    .modern-table-container .layui-table td[data-field="商家"] {
        height: 75px !important; /* 为图片预留更多空间 */
        padding: 15px !important;
    }

    /* 优化商家信息模板内的样式 */
    .modern-table-container .layui-table td[data-field="商家"] > div,
    .modern-table-container .layui-table td > div[style*="flex"] {
        display: flex !important;
        align-items: center !important;
        gap: 12px !important;
        height: 100% !important;
        overflow: visible !important;
    }

    /* 图片样式优化 */
    .modern-table-container .image-show {
        width: 45px !important;
        height: 45px !important;
        border-radius: 8px !important;
        object-fit: cover !important;
        flex-shrink: 0 !important;
        display: block !important;
        border: 2px solid #f0f0f0 !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
    }

    /* 文字内容样式 */
    .modern-table-container .layui-table td p {
        margin: 0 !important;
        line-height: 1.4 !important;
        overflow: visible !important;
        text-overflow: unset !important;
        white-space: normal !important;
        font-weight: 600 !important;
    }

    /* 移除表格滚动条 */
    .modern-table-container .layui-table-main {
        overflow: hidden !important;
    }

    .modern-table-container .layui-table-fixed {
        display: none !important;
    }

    /* 移除表格工具栏 */
    .modern-table-container .layui-table-tool {
        display: none !important;
    }

    /* 移除表格拖拽调整列宽功能 */
    .modern-table-container .layui-table-grid-down {
        display: none !important;
    }

    /* 隐藏表格右侧和底部的滚动条 */
    .modern-table-container .layui-table-main::-webkit-scrollbar,
    .modern-table-container .layui-table-body::-webkit-scrollbar {
        display: none !important;
    }

    /* 图表容器样式 */
    .modern-chart-container {
        background-color: var(--layui-card-bg-color, #fff);
        border-radius: 16px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        overflow: hidden;
        margin-bottom: 24px;
        border: 1px solid var(--layui-border-color, #e6e6e6);
    }

    .chart-header {
        padding: 20px 24px;
        background-color: var(--layui-header-bg-color, linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%));
        border-bottom: 1px solid var(--layui-border-color, #e2e8f0);
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }

    .chart-title {
        font-size: 19px;
        font-weight: 600;
        color: var(--layui-text-color-strong, #1e293b);
        margin: 0 0 4px 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .chart-title i {
        color: #3b82f6;
    }

    .chart-subtitle {
        font-size: 14px;
        color: var(--layui-text-color-secondary, #64748b);
        margin: 0;
    }

    .chart-content {
        padding: 20px;
    }

    /* 统计卡片增强样式 */
    .stat-card {
        border-radius: 16px; /* 增大圆角 */
        border: 1px solid var(--layui-border-color, #e6e6e6);
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        overflow: hidden;
        background-color: var(--layui-card-bg-color, #fff);
    }

    .stat-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.12);
    }

    .stat-card .layui-card-header {
        background-color: var(--layui-header-bg-color, linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%));
        border-bottom: 1px solid var(--layui-border-color, #e2e8f0);
        font-weight: 600;
        color: var(--layui-text-color-strong, #1e293b);
        position: relative;
        padding: 15px 20px; /* 调整内边距 */
    }

    .tips {
        position: absolute;
        right: 20px; /* 调整位置 */
        top: 50%;
        transform: translateY(-50%);
        background-color: var(--layui-badge-bg-color, #f0f9ff);
        color: var(--layui-badge-text-color, #0369a1);
        padding: 5px 10px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 500;
    }

    /* 趋势指示器 */
    .trend-indicator {
        display: inline-flex;
        align-items: center;
        gap: 4px;
        font-size: 14px;
        font-weight: 500;
        padding: 2px 6px;
        border-radius: 4px;
        margin-left: 8px;
    }

    .trend-up {
        color: #dc2626;
        background: #fef2f2;
    }

    .trend-down {
        color: #16a34a;
        background: #f0fdf4;
    }

    .red { color: #dc2626; }
    .green { color: #16a34a; }

    .text-style {
        font-weight: 600;
    }

    .data-all {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid var(--layui-border-color-light, #f3f4f6);
        font-size: 14px;
        color: var(--layui-text-color-secondary, #6b7280);
    }

    .layuiadmin-big-font {
        font-size: 36px;
        font-weight: 700;
        color: var(--layui-text-color-emphasis, #1f2937);
        text-decoration: none;
        display: block;
        margin-bottom: 10px;
        line-height: 1;
    }

    .layuiadmin-big-font:hover {
        text-decoration: none;
        color: var(--layui-link-hover-color, #3b82f6);
    }

    /* 添加页面加载动画 */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in-up {
        animation: fadeInUp 0.6s ease-out forwards;
        opacity: 0;
    }

    @keyframes slideInLeft {
        from {
            opacity: 0;
            transform: translateX(-50px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(50px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    .animate-fade-in-up {
        animation: fadeInUp 0.6s ease-out forwards;
        opacity: 0;
    }

    .animate-slide-in-left {
        animation: slideInLeft 0.6s ease-out forwards;
        opacity: 0;
    }

    .animate-slide-in-right {
        animation: slideInRight 0.6s ease-out forwards;
        opacity: 0;
    }

    /* 更新图标渐变色和动画 */
    .stat-icon.primary {
        background: var(--primary-gradient);
        animation: pulse-primary 3s ease-in-out infinite;
    }
    .stat-icon.success {
        background: var(--success-gradient);
        animation: pulse-success 3s ease-in-out infinite;
    }
    .stat-icon.warning {
        background: var(--warning-gradient);
        animation: pulse-warning 3s ease-in-out infinite;
    }
    .stat-icon.danger  {
        background: var(--danger-gradient);
        animation: pulse-danger 3s ease-in-out infinite;
    }
    .stat-icon.info    {
        background: var(--info-gradient);
        animation: pulse-info 3s ease-in-out infinite;
    }
    .stat-icon.special {
        background: var(--purple-gradient);
        animation: pulse-special 3s ease-in-out infinite;
    }
    .stat-icon.purple {
        background: linear-gradient(135deg, #8B5CFF 0%, #6A4C93 100%);
        animation: pulse-purple 3s ease-in-out infinite;
    }

    @keyframes pulse-primary {
        0%, 100% { box-shadow: 0 8px 25px rgba(103, 126, 234, 0.3); }
        50% { box-shadow: 0 8px 25px rgba(103, 126, 234, 0.6), 0 0 30px rgba(103, 126, 234, 0.4); }
    }

    @keyframes pulse-success {
        0%, 100% { box-shadow: 0 8px 25px rgba(17, 153, 142, 0.3); }
        50% { box-shadow: 0 8px 25px rgba(17, 153, 142, 0.6), 0 0 30px rgba(17, 153, 142, 0.4); }
    }

    @keyframes pulse-warning {
        0%, 100% { box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3); }
        50% { box-shadow: 0 8px 25px rgba(240, 147, 251, 0.6), 0 0 30px rgba(240, 147, 251, 0.4); }
    }

    @keyframes pulse-danger {
        0%, 100% { box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3); }
        50% { box-shadow: 0 8px 25px rgba(79, 172, 254, 0.6), 0 0 30px rgba(79, 172, 254, 0.4); }
    }

    @keyframes pulse-info {
        0%, 100% { box-shadow: 0 8px 25px rgba(67, 233, 123, 0.3); }
        50% { box-shadow: 0 8px 25px rgba(67, 233, 123, 0.6), 0 0 30px rgba(67, 233, 123, 0.4); }
    }

    @keyframes pulse-special {
        0%, 100% { box-shadow: 0 8px 25px rgba(250, 112, 154, 0.3); }
        50% { box-shadow: 0 8px 25px rgba(250, 112, 154, 0.6), 0 0 30px rgba(250, 112, 154, 0.4); }
    }

    @keyframes pulse-purple {
        0%, 100% { box-shadow: 0 8px 25px rgba(139, 92, 255, 0.3); }
        50% { box-shadow: 0 8px 25px rgba(139, 92, 255, 0.6), 0 0 30px rgba(139, 92, 255, 0.4); }
    }

    /* 优化快捷操作卡片 */
    .quick-action-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: var(--card-radius);
        padding: 30px;
        text-decoration: none;
        color: var(--layui-text-color, inherit);
        box-shadow: var(--shadow-light);
        transition: all var(--animation-speed) cubic-bezier(0.25, 0.8, 0.25, 1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        display: block;
        position: relative;
        overflow: hidden;
    }

    .quick-action-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.5s;
    }

    .quick-action-card:hover::before {
        left: 100%;
    }

    .quick-action-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: var(--shadow-heavy);
        text-decoration: none;
        color: var(--layui-text-color, inherit);
        border-color: rgba(103, 126, 234, 0.5);
    }

    /* 优化表格和图表容器 */
    .modern-table-container,
    .modern-chart-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: var(--card-radius);
        box-shadow: var(--shadow-light);
        overflow: hidden;
        margin-bottom: 30px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all var(--animation-speed) ease;
    }

    .modern-table-container:hover,
    .modern-chart-container:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-heavy);
    }

    /* 页面头部优化 */
    .layui-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: var(--card-radius);
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: var(--shadow-light);
        margin-bottom: 30px;
        overflow: hidden;
    }

    .layui-card-header {
        background: linear-gradient(135deg, rgba(103, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        padding: 25px 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }








</style>


<div class="wrapper layui-anim layui-anim-fadein">
    <script>
        // 监听主题变化并应用到iframe内部
        function applyThemeToIframe(theme) {
            const body = document.body;
            if (theme === 'dark') {
                body.classList.add('dark-theme');
                body.classList.remove('light-theme');
                // 定义深色主题下的CSS变量
                document.documentElement.style.setProperty('--layui-body-bg-color', '#1f2937');
                document.documentElement.style.setProperty('--layui-card-bg-color', '#2d3748');
                document.documentElement.style.setProperty('--layui-header-bg-color', '#2d3748');
                document.documentElement.style.setProperty('--layui-text-color', '#e2e8f0');
                document.documentElement.style.setProperty('--layui-text-color-strong', '#f7fafc');
                document.documentElement.style.setProperty('--layui-text-color-emphasis', '#f7fafc');
                document.documentElement.style.setProperty('--layui-text-color-secondary', '#a0aec0');
                document.documentElement.style.setProperty('--layui-border-color', '#4a5568');
                document.documentElement.style.setProperty('--layui-border-color-light', '#4a5568');
                document.documentElement.style.setProperty('--layui-badge-bg-color', '#4a5568');
                document.documentElement.style.setProperty('--layui-badge-text-color', '#e2e8f0');
                document.documentElement.style.setProperty('--layui-link-hover-color', '#63b3ed');
            } else {
                body.classList.add('light-theme');
                body.classList.remove('dark-theme');
                // 恢复或设置浅色主题下的CSS变量
                document.documentElement.style.setProperty('--layui-body-bg-color', '#f8fafc');
                document.documentElement.style.setProperty('--layui-card-bg-color', '#fff');
                document.documentElement.style.setProperty('--layui-header-bg-color', 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)');
                document.documentElement.style.setProperty('--layui-text-color', '#333');
                document.documentElement.style.setProperty('--layui-text-color-strong', '#1e293b');
                document.documentElement.style.setProperty('--layui-text-color-emphasis', '#1f2937');
                document.documentElement.style.setProperty('--layui-text-color-secondary', '#6b7280');
                document.documentElement.style.setProperty('--layui-border-color', '#e6e6e6');
                document.documentElement.style.setProperty('--layui-border-color-light', '#f3f4f6');
                document.documentElement.style.setProperty('--layui-badge-bg-color', '#f0f9ff');
                document.documentElement.style.setProperty('--layui-badge-text-color', '#0369a1');
                document.documentElement.style.setProperty('--layui-link-hover-color', '#3b82f6');
            }
        }

        // 监听来自父页面的消息
        window.addEventListener('message', function(event) {
            // 为了安全，可以检查event.origin是否是预期的父页面来源
            // if (event.origin !== 'http://your-parent-domain.com') return;
            if (event.data && event.data.type === 'themeChange') {
                applyThemeToIframe(event.data.theme);
            }
        });

        // 页面加载时，尝试从父页面获取当前主题
        if (window.parent && window.parent !== window) {
            try {
                 // 请求父页面的当前主题状态
                window.parent.postMessage({ type: 'requestThemeStatus', iframeId: window.name || null }, '*');
            } catch (e) {
                console.error('Error requesting theme status from parent:', e);
            }
        }
    </script>
    <!-- 页面头部 -->
    <div class="layui-card">
        <div class="layui-card-header">
            <span class="header-font">运营数据</span>
            <span class="header-time">更新时间：{$res.time}</span>
        </div>
    </div>

    <!-- 核心业务数据统计卡片 -->
    <div class="stats-grid">
        <!-- 第一行：核心业务数据 -->
        <div class="modern-stat-card" onclick="openPage('{:url(\"order.order/lists\")}', '38', '成交订单')">
            <div class="stat-icon success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-header">
                <h3 class="stat-title">成交订单（笔）</h3>
                <span class="stat-badge">今日</span>
            </div>
            <div class="stat-value">{$res.data.order_num.today}</div>
            <div class="stat-change">
                <span class="yesterday-label">昨日：{$res.data.order_num.yesterday}</span>
                {if ($res.data.order_num.change_red > 0) }
                <span class="change-indicator change-down">
                    <i class="fas fa-arrow-down"></i>
                    {$res.data.order_num.change_red}
                </span>
                {else/}
                <span class="change-indicator change-up">
                    <i class="fas fa-arrow-up"></i>
                    {$res.data.order_num.change_add}
                </span>
                {/if}
            </div>
            <div class="stat-footer">
                <span>累计成交订单</span>
                <span class="stat-total">{$res.data.order_num.all_num}</span>
            </div>
        </div>

        <div class="modern-stat-card" onclick="openPage('{:url(\"order.order/lists\")}', '38', '营业额详情')">
            <div class="stat-icon primary">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="stat-header">
                <h3 class="stat-title">营业额（元）</h3>
                <span class="stat-badge">今日</span>
            </div>
            <div class="stat-value">{$res.data.order_price.today}</div>
            <div class="stat-change">
                <span class="yesterday-label">昨日：{$res.data.order_price.yesterday}</span>
                {if ($res.data.order_price.change_red > 0) }
                <span class="change-indicator change-down">
                    <i class="fas fa-arrow-down"></i>
                    {$res.data.order_price.change_red}
                </span>
                {else/}
                <span class="change-indicator change-up">
                    <i class="fas fa-arrow-up"></i>
                    {$res.data.order_price.change_add}
                </span>
                {/if}
            </div>
            <div class="stat-footer">
                <span>累计营业额</span>
                <span class="stat-total">{$res.data.order_price.all_price}</span>
            </div>
        </div>

        <div class="modern-stat-card" onclick="openPage('{:url(\"user.user/lists\")}', '34', '会员管理')">
            <div class="stat-icon info">
                <i class="fas fa-user-plus"></i>
            </div>
            <div class="stat-header">
                <h3 class="stat-title">新增会员（人）</h3>
                <span class="stat-badge">今日</span>
            </div>
            <div class="stat-value">{$res.data.add_user_num.today}</div>
            <div class="stat-change">
                <span class="yesterday-label">昨日：{$res.data.add_user_num.yesterday}</span>
                {if ($res.data.add_user_num.change_red > 0) }
                <span class="change-indicator change-down">
                    <i class="fas fa-arrow-down"></i>
                    {$res.data.add_user_num.change_red}
                </span>
                {else/}
                <span class="change-indicator change-up">
                    <i class="fas fa-arrow-up"></i>
                    {$res.data.add_user_num.change_add}
                </span>
                {/if}
            </div>
            <div class="stat-footer">
                <span>会员总数</span>
                <span class="stat-total">{$res.data.add_user_num.all_num}</span>
            </div>
        </div>

        <!-- 会员访问量 -->
        <div class="modern-stat-card" onclick="openPage('{:url(\"Statistics/visit\")}', '178', '访问统计')">
            <div class="stat-icon warning">
                <i class="fas fa-eye"></i>
            </div>
            <div class="stat-header">
                <h3 class="stat-title">会员访问量</h3>
                <span class="stat-badge">今日</span>
            </div>
            <div class="stat-value">{$res.data.visit_user_num.today}</div>
            <div class="stat-change">
                <span class="yesterday-label">昨日：{$res.data.visit_user_num.yesterday}</span>
                {if ($res.data.visit_user_num.change_red > 0) }
                <span class="change-indicator change-down">
                    <i class="fas fa-arrow-down"></i>
                    {$res.data.visit_user_num.change_red}
                </span>
                {else/}
                <span class="change-indicator change-up">
                    <i class="fas fa-arrow-up"></i>
                    {$res.data.visit_user_num.change_add}
                </span>
                {/if}
            </div>
            <div class="stat-footer">
                <span>累计访问量</span>
                <span class="stat-total">{$res.data.visit_user_num.all_num}</span>
            </div>
        </div>

        <!-- 待发货订单 -->
        <div class="modern-stat-card" onclick="openPage('{:url(\"order.order/lists\", [\"order_status\" => 1])}', '38', '待发货订单')">
            <div class="stat-icon danger">
                <i class="fas fa-truck"></i>
            </div>
            <div class="stat-header">
                <h3 class="stat-title">待发货订单</h3>
                <span class="stat-badge">今日</span>
            </div>
            <div class="stat-value">{$res.data.order_unshipped_num.today}</div>
            <div class="stat-change">
                <span class="yesterday-label">昨日：{$res.data.order_unshipped_num.yesterday}</span>
                {if ($res.data.order_unshipped_num.change_red > 0) }
                <span class="change-indicator change-down">
                    <i class="fas fa-arrow-down"></i>
                    {$res.data.order_unshipped_num.change_red}
                </span>
                {else/}
                <span class="change-indicator change-up">
                    <i class="fas fa-arrow-up"></i>
                    {$res.data.order_unshipped_num.change_add}
                </span>
                {/if}
            </div>
            <div class="stat-footer">
                <span>待发货总数</span>
                <span class="stat-total">{$res.data.order_unshipped_num.all_num}</span>
            </div>
        </div>

        <!-- 商圈数量 -->
        <div class="modern-stat-card" onclick="openPage('{:url(\"community.community_article/lists\")}', '239', '商圈管理')">
            <div class="stat-icon primary">
                <i class="fas fa-store"></i>
            </div>
            <div class="stat-header">
                <h3 class="stat-title">商圈数量</h3>
                <span class="stat-badge">今日</span>
            </div>
            <div class="stat-value">{$res.data.community_num.today}</div>
            <div class="stat-change">
                <span class="yesterday-label">昨日：{$res.data.community_num.yesterday}</span>
                {if ($res.data.community_num.change_red > 0) }
                <span class="change-indicator change-down">
                    <i class="fas fa-arrow-down"></i>
                    {$res.data.community_num.change_red}
                </span>
                {else/}
                <span class="change-indicator change-up">
                    <i class="fas fa-arrow-up"></i>
                    {$res.data.community_num.change_add}
                </span>
                {/if}
            </div>
            <div class="stat-footer">
                <span>商圈总数</span>
                <span class="stat-total">{$res.data.community_num.all_num}</span>
            </div>
        </div>

        <!-- 代理数量 -->
        <div class="modern-stat-card" onclick="openPage('{:url(\"agent.agent/index\")}', '469', '代理管理')">
            <div class="stat-icon info">
                <i class="fas fa-user-tie"></i>
            </div>
            <div class="stat-header">
                <h3 class="stat-title">代理数量</h3>
                <span class="stat-badge">今日</span>
            </div>
            <div class="stat-value">{$res.data.agent_num.today}</div>
            <div class="stat-change">
                <span class="yesterday-label">昨日：{$res.data.agent_num.yesterday}</span>
                {if ($res.data.agent_num.change_red > 0) }
                <span class="change-indicator change-down">
                    <i class="fas fa-arrow-down"></i>
                    {$res.data.agent_num.change_red}
                </span>
                {else/}
                <span class="change-indicator change-up">
                    <i class="fas fa-arrow-up"></i>
                    {$res.data.agent_num.change_add}
                </span>
                {/if}
            </div>
            <div class="stat-footer">
                <span>代理总数</span>
                <span class="stat-total">{$res.data.agent_num.all_num}</span>
            </div>
        </div>

        <!-- 商家数量 -->
        <div class="modern-stat-card" onclick="openPage('{:url(\"shop.store/lists\")}', '148', '商家管理')">
            <div class="stat-icon warning">
                <i class="fas fa-store-alt"></i>
            </div>
            <div class="stat-header">
                <h3 class="stat-title">商家数量</h3>
                <span class="stat-badge">今日</span>
            </div>
            <div class="stat-value">{$res.data.shop_num.today}</div>
            <div class="stat-change">
                <span class="yesterday-label">昨日：{$res.data.shop_num.yesterday}</span>
                {if ($res.data.shop_num.change_red > 0) }
                <span class="change-indicator change-down">
                    <i class="fas fa-arrow-down"></i>
                    {$res.data.shop_num.change_red}
                </span>
                {else/}
                <span class="change-indicator change-up">
                    <i class="fas fa-arrow-up"></i>
                    {$res.data.shop_num.change_add}
                </span>
                {/if}
            </div>
            <div class="stat-footer">
                <span>商家总数</span>
                <span class="stat-total">{$res.data.shop_num.all_num}</span>
            </div>
        </div>

        <!-- 商品数量 -->
        <div class="modern-stat-card" onclick="openPage('{:url(\"goods.goods/lists\")}', '44', '商品管理')">
            <div class="stat-icon special">
                <i class="fas fa-boxes"></i>
            </div>
            <div class="stat-header">
                <h3 class="stat-title">商品数量</h3>
                <span class="stat-badge">今日</span>
            </div>
            <div class="stat-value">{$res.data.goods_num.today}</div>
            <div class="stat-change">
                <span class="yesterday-label">昨日：{$res.data.goods_num.yesterday}</span>
                {if ($res.data.goods_num.change_red > 0) }
                <span class="change-indicator change-down">
                    <i class="fas fa-arrow-down"></i>
                    {$res.data.goods_num.change_red}
                </span>
                {else/}
                <span class="change-indicator change-up">
                    <i class="fas fa-arrow-up"></i>
                    {$res.data.goods_num.change_add}
                </span>
                {/if}
            </div>
            <div class="stat-footer">
                <span>在售商品总数</span>
                <span class="stat-total">{$res.data.goods_num.all_num}</span>
            </div>
        </div>

        <!-- 售后申请 -->
        <div class="modern-stat-card" onclick="openPage('{:url(\"after_sale.after_sale/lists\")}', '45', '售后管理')">
            <div class="stat-icon purple">
                <i class="fas fa-undo-alt"></i>
            </div>
            <div class="stat-header">
                <h3 class="stat-title">售后申请</h3>
                <span class="stat-badge">今日</span>
            </div>
            <div class="stat-value">{$res.data.aftersale_num.today}</div>
            <div class="stat-change">
                <span class="yesterday-label">昨日：{$res.data.aftersale_num.yesterday}</span>
                {if ($res.data.aftersale_num.change_red > 0) }
                <span class="change-indicator change-down">
                    <i class="fas fa-arrow-down"></i>
                    {$res.data.aftersale_num.change_red}
                </span>
                {else/}
                <span class="change-indicator change-up">
                    <i class="fas fa-arrow-up"></i>
                    {$res.data.aftersale_num.change_add}
                </span>
                {/if}
            </div>
            <div class="stat-footer">
                <span>待处理售后</span>
                <span class="stat-total">{$res.data.aftersale_num.all_num}</span>
            </div>
        </div>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions">
        <a href="javascript:void(0)" class="quick-action-card" onclick="openPage('{:url(\"goods.goods/lists\")}', '44', '商品管理')">
            <div class="quick-action-icon primary">
                <i class="fas fa-box"></i>
            </div>
            <h4 class="quick-action-title">商品管理</h4>
            <p class="quick-action-desc">管理商品信息和库存</p>
        </a>
        <a href="javascript:void(0)" class="quick-action-card" onclick="openPage('{:url(\"order.order/lists\")}', '38', '订单管理')">
            <div class="quick-action-icon success">
                <i class="fas fa-receipt"></i>
            </div>
            <h4 class="quick-action-title">订单管理</h4>
            <p class="quick-action-desc">处理订单和发货</p>
        </a>
        <a href="javascript:void(0)" class="quick-action-card" onclick="openPage('{:url(\"user.user/lists\")}', '34', '会员管理')">
            <div class="quick-action-icon info">
                <i class="fas fa-users"></i>
            </div>
            <h4 class="quick-action-title">会员管理</h4>
            <p class="quick-action-desc">管理用户信息</p>
        </a>
        <!-- <a href="javascript:void(0)" class="quick-action-card" onclick="openPage('{:url('coupon.shop_coupon/lists')}', '39', '优惠券管理')">
            <div class="quick-action-icon warning">
                <i class="fas fa-ticket-alt"></i>
            </div>
            <h4 class="quick-action-title">优惠券</h4>
            <p class="quick-action-desc">创建和管理优惠券</p>
        </a> -->
        <!-- <a href="javascript:void(0)" class="quick-action-card" onclick="openPage('{:url('seckill.seckill_goods/lists')}', '36', '限时秒杀')">
            <div class="quick-action-icon danger">
                <i class="fas fa-bolt"></i>
            </div>
            <h4 class="quick-action-title">限时秒杀</h4>
            <p class="quick-action-desc">设置秒杀活动</p>
        </a> -->
        <!-- <a href="javascript:void(0)" class="quick-action-card" onclick="openPage('{:url('distribution.member/index')}', '81', '分销管理')">
            <div class="quick-action-icon primary">
                <i class="fas fa-share-alt"></i>
            </div>
            <h4 class="quick-action-title">分销管理</h4>
            <p class="quick-action-desc">管理分销体系</p>
        </a> -->
        <a href="javascript:void(0)" class="quick-action-card" onclick="openPage('{:url(\"content.help/lists\")}', '70', '内容管理')">
            <div class="quick-action-icon info">
                <i class="fas fa-file-alt"></i>
            </div>
            <h4 class="quick-action-title">内容管理</h4>
            <p class="quick-action-desc">管理帮助和内容</p>
        </a>
        <a href="javascript:void(0)" class="quick-action-card" onclick="openPage('{:url(\"finance.finance/center\")}', '226', '财务中心')">
            <div class="quick-action-icon success">
                <i class="fas fa-chart-line"></i>
            </div>
            <h4 class="quick-action-title">财务中心</h4>
            <p class="quick-action-desc">查看财务报表</p>
        </a>
    </div>

    <!-- 商家排行榜 -->
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 32px;">
        <!-- 销冠商家 -->
        <div class="modern-table-container">
            <div class="table-header">
                <h3 class="table-title">
                    <i class="fas fa-trophy"></i>
                    销冠商家排行榜
                </h3>
                <span class="stat-badge">前5名</span>
            </div>
            <div class="table-content">
                <table id="goods-lists" lay-filter="goods-lists"></table>
                <script type="text/html" id="goods-info">
                    <div style="display: flex; align-items: center; gap: 12px; padding: 8px 0; height: 100%; overflow: visible;">
                        <img src="{{ d.logo }}" style="width: 45px; height: 45px; border-radius: 8px; object-fit: cover; flex-shrink: 0; border: 2px solid #f0f0f0; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" class="image-show">
                        <div style="flex: 1; overflow: visible;">
                            <p style="margin: 0; font-weight: 600; color: #333; font-size: 14px; line-height: 1.4; overflow: visible; white-space: normal;">{{d.name}}</p>
                            <p style="margin: 2px 0 0 0; font-size: 12px; color: #999; line-height: 1.2;">销售冠军</p>
                        </div>
                    </div>
                </script>
            </div>
        </div>

        <!-- 人气商家 -->
        <div class="modern-table-container">
            <div class="table-header">
                <h3 class="table-title">
                    <i class="fas fa-fire"></i>
                    人气商家排行榜
                </h3>
                <span class="stat-badge">前5名</span>
            </div>
            <div class="table-content">
                <table id="goods-lists2" lay-filter="goods-lists2"></table>
                <script type="text/html" id="goods-info2">
                    <div style="display: flex; align-items: center; gap: 12px; padding: 8px 0; height: 100%; overflow: visible;">
                        <img src="{{ d.logo }}" style="width: 45px; height: 45px; border-radius: 8px; object-fit: cover; flex-shrink: 0; border: 2px solid #f0f0f0; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" class="image-show">
                        <div style="flex: 1; overflow: visible;">
                            <p style="margin: 0; font-weight: 600; color: #333; font-size: 14px; line-height: 1.4; overflow: visible; white-space: normal;">{{d.name}}</p>
                            <p style="margin: 2px 0 0 0; font-size: 12px; color: #999; line-height: 1.2;">人气之星</p>
                        </div>
                    </div>
                </script>
            </div>
        </div>
    </div>

    <!-- 数据趋势图表 -->
    <div class="modern-chart-container">
        <div class="chart-header">
            <div>
                <h3 class="chart-title">
                    <i class="fas fa-chart-line"></i>
                    营业额趋势分析
                </h3>
                <p class="chart-subtitle">近15天营业额变化趋势，实时监控业务增长</p>
            </div>
            <div class="stat-badge">近15天</div>
        </div>
        <div class="chart-content">
            <div id="sale-charts" style="width: 100%; height: 400px;">
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: var(--text-muted);">
                    <i class="fas fa-spinner fa-spin" style="margin-right: 8px;"></i>
                    数据加载中...
                </div>
            </div>
        </div>
    </div>

    <div class="modern-chart-container">
        <div class="chart-header">
            <div>
                <h3 class="chart-title">
                    <i class="fas fa-users"></i>
                    会员访问量趋势
                </h3>
                <p class="chart-subtitle">近15天会员访问量变化，了解用户活跃度</p>
            </div>
            <div class="stat-badge">近15天</div>
        </div>
        <div class="chart-content">
            <div id="user-charts" style="width: 100%; height: 400px;">
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: var(--text-muted);">
                    <i class="fas fa-spinner fa-spin" style="margin-right: 8px;"></i>
                    数据加载中...
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<footer class="info_footer">
    {$company_name}&nbsp;&nbsp;|&nbsp;&nbsp;版本号：{$version}
    <br><br>
</footer>
<script>
    console.log("[stat.html] Script block started."); // 日志 1: 脚本块开始


    

    // 页面跳转函数
    function openPage(url, id, title) {
        console.log("[stat.html] openPage called:", url, id, title);

        // 调用父窗口的openTabsPage函数
        if (window.parent && window.parent.openTabsPage) {
            console.log("[stat.html] 调用父窗口的openTabsPage函数");
            window.parent.openTabsPage(url, title, id);
        } else {
            console.error("[stat.html] 父窗口或openTabsPage函数未找到");
            // 如果无法找到父窗口函数，尝试直接跳转
            window.location.href = url;
        }
    }

    // 添加页面加载动画
    document.addEventListener('DOMContentLoaded', function() {
        // 为统计卡片添加延迟动画
        const statCards = document.querySelectorAll('.modern-stat-card');
        statCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('animate-fade-in-up');
        });

        // 为快捷操作卡片添加延迟动画
        const quickActions = document.querySelectorAll('.quick-action-card');
        quickActions.forEach((card, index) => {
            card.style.animationDelay = `${(statCards.length + index) * 0.1}s`;
            card.classList.add('animate-fade-in-up');
        });

        // 为图表容器添加动画
        const chartContainers = document.querySelectorAll('.modern-chart-container, .modern-table-container');
        chartContainers.forEach((container, index) => {
            container.style.animationDelay = `${(statCards.length + quickActions.length + index) * 0.1}s`;
            container.classList.add('animate-fade-in-up');
        });
    });

    layui.config({
        version:"{$front_version}",
        base: '/static/lib/'
    }).extend({
        echarts: 'echarts/echarts',
        echartsTheme: 'echarts/echartsTheme',
    }).use(['echarts','form','element', 'echartsTheme'], function () {
        console.log("[stat.html] layui.use callback started."); // 日志 2: layui.use 回调开始
        var $ = layui.$;
        var echarts = layui.echarts;

        // 定义主题色和渐变色
        let bgColor = "#fff";
        let themeColors = {
            primary: "#1E9FFF",
            success: "#36CE9E",
            warning: "#FFB800",
            danger: "#FF5722",
            purple: "#8B5CFF",
            cyan: "#00C9C9"
        };

        // 图表颜色数组
        let color = [
            themeColors.primary,
            themeColors.success,
            themeColors.warning,
            themeColors.danger,
            themeColors.purple,
            themeColors.cyan
        ];

        // 十六进制颜色转RGBA
        const hexToRgba = (hex, opacity) => {
            let rgbaColor = "";
            let reg = /^#[\da-f]{6}$/i;
            if (reg.test(hex)) {
                rgbaColor = `rgba(${parseInt("0x" + hex.slice(1, 3))},${parseInt(
                    "0x" + hex.slice(3, 5)
                )},${parseInt("0x" + hex.slice(5, 7))},${opacity})`;
            }
            return rgbaColor;
        }

        // 图表公共配置
        const chartCommonOptions = {
            backgroundColor: bgColor,
            color: color,
            textStyle: {
                fontFamily: "'Microsoft YaHei', Arial, sans-serif"
            },
            tooltip: {
                trigger: "axis",
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                borderColor: '#f2f2f2',
                borderWidth: 1,
                padding: [10, 15],
                textStyle: {
                    color: '#333',
                    fontSize: 13
                },
                extraCssText: 'box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); border-radius: 4px;',
                axisPointer: {
                    type: 'line',
                    lineStyle: {
                        color: 'rgba(0, 144, 255, 0.3)',
                        width: 2
                    }
                }
            },
            grid: {
                top: 80,
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: "category",
                boundaryGap: false,
                axisLabel: {
                    fontSize: 12,
                    color: "#666",
                    margin: 15
                },
                axisLine: {
                    lineStyle: {
                        color: "#E9E9E9"
                    }
                },
                axisTick: {
                    show: false
                }
            },
            yAxis: {
                type: "value",
                axisLabel: {
                    fontSize: 12,
                    color: "#666",
                    margin: 15
                },
                splitLine: {
                    lineStyle: {
                        type: "dashed",
                        color: "#E9E9E9"
                    }
                },
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                }
            }
        };

        like.ajax({
            url: '{:url("index/stat")}',
            type: "get",
            success: function (res) {
                var dates = res.data.dates,
                    echarts_order_amount = res.data.echarts_order_amount,
                    echarts_user_visit = res.data.echarts_user_visit;

                // 营业额图表配置
                var sale_option = Object.assign({}, chartCommonOptions, {
                    title: {
                        text: '营业额趋势分析',
                        subtext: '近15天数据',
                        left: 'center',
                        top: 20,
                        textStyle: {
                            color: '#333',
                            fontSize: 18,
                            fontWeight: 'normal'
                        },
                        subtextStyle: {
                            color: '#999',
                            fontSize: 12
                        }
                    },
                    legend: {
                        data: ['营业额'],
                        icon: 'roundRect',
                        right: 20,
                        top: 20
                    },
                    tooltip: {
                        formatter: function(params) {
                            return `<div style="font-weight:bold;margin-bottom:5px;">${params[0].name}</div>
                                    <div style="display:flex;align-items:center;margin-bottom:3px;">
                                        <span style="display:inline-block;margin-right:8px;width:10px;height:10px;border-radius:50%;background-color:${themeColors.primary};"></span>
                                        <span>营业额：</span>
                                        <span style="font-weight:bold;margin-left:5px;color:${themeColors.primary};">${params[0].value} 元</span>
                                    </div>`;
                        }
                    },
                    xAxis: {
                        data: dates
                    },
                    yAxis: {
                        name: '营业额 (元)',
                        nameTextStyle: {
                            color: "#666",
                            fontSize: 12,
                            padding: [0, 0, 0, 40]
                        }
                    },
                    series: [{
                        name: '营业额',
                        type: "line",
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 8,
                        showSymbol: false,
                        lineStyle: {
                            width: 4,
                            color: themeColors.primary,
                            shadowBlur: 10,
                            shadowColor: hexToRgba(themeColors.primary, 0.5),
                            shadowOffsetY: 5,
                            cap: 'round'
                        },
                        itemStyle: {
                            color: themeColors.primary,
                            borderWidth: 2,
                            borderColor: '#fff',
                            shadowBlur: 5,
                            shadowColor: hexToRgba(themeColors.primary, 0.5)
                        },
                        emphasis: {
                            scale: true
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(
                                0, 0, 0, 1,
                                [
                                    {offset: 0, color: hexToRgba(themeColors.primary, 0.5)},
                                    {offset: 0.5, color: hexToRgba(themeColors.primary, 0.2)},
                                    {offset: 1, color: hexToRgba(themeColors.primary, 0.05)}
                                ]
                            )
                        },
                        data: echarts_order_amount
                    }]
                });

                // 会员访问量图表配置
                var user_option = Object.assign({}, chartCommonOptions, {
                    title: {
                        text: '会员访问量趋势分析',
                        subtext: '近15天数据',
                        left: 'center',
                        top: 20,
                        textStyle: {
                            color: '#333',
                            fontSize: 18,
                            fontWeight: 'normal'
                        },
                        subtextStyle: {
                            color: '#999',
                            fontSize: 12
                        }
                    },
                    legend: {
                        data: ['访问量'],
                        icon: 'roundRect',
                        right: 20,
                        top: 20
                    },
                    tooltip: {
                        formatter: function(params) {
                            return `<div style="font-weight:bold;margin-bottom:5px;">${params[0].name}</div>
                                    <div style="display:flex;align-items:center;margin-bottom:3px;">
                                        <span style="display:inline-block;margin-right:8px;width:10px;height:10px;border-radius:50%;background-color:${themeColors.danger};"></span>
                                        <span>访问量：</span>
                                        <span style="font-weight:bold;margin-left:5px;color:${themeColors.danger};">${params[0].value} 人</span>
                                    </div>`;
                        }
                    },
                    xAxis: {
                        data: dates
                    },
                    yAxis: {
                        name: '访问量 (人)',
                        nameTextStyle: {
                            color: "#666",
                            fontSize: 12,
                            padding: [0, 0, 0, 40]
                        }
                    },
                    series: [{
                        name: '访问量',
                        type: "line",
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 8,
                        showSymbol: false,
                        lineStyle: {
                            width: 4,
                            color: themeColors.danger,
                            shadowBlur: 10,
                            shadowColor: hexToRgba(themeColors.danger, 0.5),
                            shadowOffsetY: 5,
                            cap: 'round'
                        },
                        itemStyle: {
                            color: themeColors.danger,
                            borderWidth: 2,
                            borderColor: '#fff',
                            shadowBlur: 5,
                            shadowColor: hexToRgba(themeColors.danger, 0.5)
                        },
                        emphasis: {
                            scale: true
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(
                                0, 0, 0, 1,
                                [
                                    {offset: 0, color: hexToRgba(themeColors.danger, 0.5)},
                                    {offset: 0.5, color: hexToRgba(themeColors.danger, 0.2)},
                                    {offset: 1, color: hexToRgba(themeColors.danger, 0.05)}
                                ]
                            )
                        },
                        data: echarts_user_visit
                    }]
                });


                // Store chart options globally to allow updates
                window.saleChartOptions = sale_option;
                window.userChartOptions = user_option;

                // 初始化图表并保存到全局变量
                window.saleChart = echarts.init(document.getElementById('sale-charts'));
                window.userChart = echarts.init(document.getElementById('user-charts'));

                // 设置图表配置
                window.saleChart.setOption(window.saleChartOptions, true);
                window.userChart.setOption(window.userChartOptions, true);

                // After initial chart setup, request theme to apply it correctly
                if (window.parent) {
                    console.log('[stat.html] Requesting initial theme status after charts are ready.');
                    window.parent.postMessage({ type: 'requestThemeStatus' }, '*');
                }

                // 窗口大小变化时，重新调整图表大小
                window.addEventListener('resize', function() {
                    window.saleChart.resize();
                    window.userChart.resize();
                });
            }
        });

        // 初始加载商家数据
        getList();

        function getList() {
            var type = 1;
            like.tableLists('#goods-lists', '{:url("index/shop")}?type='+type, [
                {field: 'number',title: '排名',width:'15%'}
                ,{title: '商家',width:'55%', align: 'center', templet: '#goods-info'}
                ,{field: 'order_amount', title: '销售金额',width:'30%'}
            ]);
            var type = 2;
            like.tableLists('#goods-lists2', '{:url("index/shop")}?type='+type, [
                {field: 'number',title: '排名',width:'15%'}
                ,{title: '商家',width:'55%', align: 'center', templet: '#goods-info2'}
                ,{field: 'visited_num', title: '进店人数',width:'30%'}
            ]);
        }

    });

    // 主题同步逻辑
    document.addEventListener('DOMContentLoaded', function () {
        // 定义主题颜色映射
        const themeColorMaps = {
            light: {
                '--theme-primary': '#007bff',
                '--theme-danger': '#dc3545',
                '--text-color': '#333',
                '--text-color-secondary': '#666',
                '--text-color-tertiary': '#999',
                '--bg-color': '#ffffff',
                '--bg-color-secondary': '#f8f9fa',
                '--border-color': '#dee2e6',
                '--chart-title-color': '#333',
                '--chart-subtitle-color': '#999',
                '--chart-axis-label-color': '#666',
                '--chart-axis-line-color': '#E9E9E9',
                '--chart-split-line-color': '#E9E9E9',
                '--chart-tooltip-bg': 'rgba(255, 255, 255, 0.9)',
                '--chart-tooltip-border': '#f2f2f2',
                '--chart-tooltip-text': '#333',
            },
            dark: {
                '--theme-primary': '#4dabf7',
                '--theme-danger': '#ff6b6b',
                '--text-color': '#e9ecef',
                '--text-color-secondary': '#adb5bd',
                '--text-color-tertiary': '#868e96',
                '--bg-color': '#212529',
                '--bg-color-secondary': '#343a40',
                '--border-color': '#495057',
                '--chart-title-color': '#e9ecef',
                '--chart-subtitle-color': '#adb5bd',
                '--chart-axis-label-color': '#adb5bd',
                '--chart-axis-line-color': '#495057',
                '--chart-split-line-color': '#495057',
                '--chart-tooltip-bg': 'rgba(33, 37, 41, 0.9)',
                '--chart-tooltip-border': '#495057',
                '--chart-tooltip-text': '#e9ecef',
            },
            // Add other themes here if needed, e.g., purple, green, blue
            purple: { // Example for a purple theme
                '--theme-primary': '#845ef7',
                '--theme-danger': '#f06595',
                '--text-color': '#e9ecef',
                '--text-color-secondary': '#adb5bd',
                '--text-color-tertiary': '#868e96',
                '--bg-color': '#362255',
                '--bg-color-secondary': '#49336b',
                '--border-color': '#5f4583',
                '--chart-title-color': '#e9ecef',
                '--chart-subtitle-color': '#adb5bd',
                '--chart-axis-label-color': '#adb5bd',
                '--chart-axis-line-color': '#5f4583',
                '--chart-split-line-color': '#5f4583',
                '--chart-tooltip-bg': 'rgba(54, 34, 85, 0.9)',
                '--chart-tooltip-border': '#5f4583',
                '--chart-tooltip-text': '#e9ecef',
            },
            green: { // Example for a green theme
                '--theme-primary': '#20c997',
                '--theme-danger': '#ff922b',
                '--text-color': '#e9ecef',
                '--text-color-secondary': '#adb5bd',
                '--text-color-tertiary': '#868e96',
                '--bg-color': '#1a3b32',
                '--bg-color-secondary': '#265042',
                '--border-color': '#366856',
                '--chart-title-color': '#e9ecef',
                '--chart-subtitle-color': '#adb5bd',
                '--chart-axis-label-color': '#adb5bd',
                '--chart-axis-line-color': '#366856',
                '--chart-split-line-color': '#366856',
                '--chart-tooltip-bg': 'rgba(26, 59, 50, 0.9)',
                '--chart-tooltip-border': '#366856',
                '--chart-tooltip-text': '#e9ecef',
            },
            blue: { // Example for a blue theme
                '--theme-primary': '#339af0',
                '--theme-danger': '#f76707',
                '--text-color': '#e9ecef',
                '--text-color-secondary': '#adb5bd',
                '--text-color-tertiary': '#868e96',
                '--bg-color': '#193047',
                '--bg-color-secondary': '#23405f',
                '--border-color': '#305277',
                '--chart-title-color': '#e9ecef',
                '--chart-subtitle-color': '#adb5bd',
                '--chart-axis-label-color': '#adb5bd',
                '--chart-axis-line-color': '#305277',
                '--chart-split-line-color': '#305277',
                '--chart-tooltip-bg': 'rgba(25, 48, 71, 0.9)',
                '--chart-tooltip-border': '#305277',
                '--chart-tooltip-text': '#e9ecef',
            }
        };

        function applyTheme(themeName) {
            const root = document.documentElement;
            const colors = themeColorMaps[themeName] || themeColorMaps.light; // Default to light if theme not found

            for (const [variable, value] of Object.entries(colors)) {
                root.style.setProperty(variable, value);
            }
            console.log('[stat.html] Applied theme:', themeName, colors);

            // Update ECharts instances if they exist
            if (window.saleChart && window.userChart) {
                console.log('[stat.html] Re-rendering charts for theme:', themeName);
                // Update chart options with new theme colors
                // This requires chart options to be structured to use CSS variables or be dynamically updated
                // For simplicity, we'll re-initialize with potentially updated global `themeColors` if they are set by CSS vars
                // Or, more robustly, update specific color options in sale_option and user_option then call setOption

                // Example: Update chart colors based on CSS variables
                const newThemeColors = {
                    primary: getComputedStyle(root).getPropertyValue('--theme-primary').trim(),
                    danger: getComputedStyle(root).getPropertyValue('--theme-danger').trim(),
                    chartTitleColor: getComputedStyle(root).getPropertyValue('--chart-title-color').trim(),
                    chartSubtitleColor: getComputedStyle(root).getPropertyValue('--chart-subtitle-color').trim(),
                    chartAxisLabelColor: getComputedStyle(root).getPropertyValue('--chart-axis-label-color').trim(),
                    chartAxisLineColor: getComputedStyle(root).getPropertyValue('--chart-axis-line-color').trim(),
                    chartSplitLineColor: getComputedStyle(root).getPropertyValue('--chart-split-line-color').trim(),
                    chartTooltipBg: getComputedStyle(root).getPropertyValue('--chart-tooltip-bg').trim(),
                    chartTooltipBorder: getComputedStyle(root).getPropertyValue('--chart-tooltip-border').trim(),
                    chartTooltipText: getComputedStyle(root).getPropertyValue('--chart-tooltip-text').trim(),
                };

                // Update sale chart options
                if (window.saleChartOptions) { // Assuming sale_option is stored globally or accessible
                    window.saleChartOptions.title.textStyle.color = newThemeColors.chartTitleColor;
                    window.saleChartOptions.title.subtextStyle.color = newThemeColors.chartSubtitleColor;
                    window.saleChartOptions.legend.textStyle.color = newThemeColors.chartAxisLabelColor;
                    window.saleChartOptions.xAxis.axisLabel.color = newThemeColors.chartAxisLabelColor;
                    window.saleChartOptions.xAxis.axisLine.lineStyle.color = newThemeColors.chartAxisLineColor;
                    window.saleChartOptions.yAxis.nameTextStyle.color = newThemeColors.chartAxisLabelColor;
                    window.saleChartOptions.yAxis.axisLabel.color = newThemeColors.chartAxisLabelColor;
                    window.saleChartOptions.yAxis.splitLine.lineStyle.color = newThemeColors.chartSplitLineColor;
                    window.saleChartOptions.series[0].lineStyle.color = newThemeColors.primary;
                    window.saleChartOptions.series[0].itemStyle.color = newThemeColors.primary;
                    window.saleChartOptions.series[0].areaStyle.color.colorStops[0].color = hexToRgba(newThemeColors.primary, 0.5);
                    window.saleChartOptions.series[0].areaStyle.color.colorStops[1].color = hexToRgba(newThemeColors.primary, 0.2);
                    window.saleChartOptions.series[0].areaStyle.color.colorStops[2].color = hexToRgba(newThemeColors.primary, 0.05);
                    window.saleChartOptions.tooltip.backgroundColor = newThemeColors.chartTooltipBg;
                    window.saleChartOptions.tooltip.borderColor = newThemeColors.chartTooltipBorder;
                    window.saleChartOptions.tooltip.textStyle.color = newThemeColors.chartTooltipText;
                    window.saleChartOptions.tooltip.formatter = function(params) {
                        return `<div style="font-weight:bold;margin-bottom:5px;color:${newThemeColors.chartTooltipText};">${params[0].name}</div>
                                <div style="display:flex;align-items:center;margin-bottom:3px;">
                                    <span style="display:inline-block;margin-right:8px;width:10px;height:10px;border-radius:50%;background-color:${newThemeColors.primary};"></span>
                                    <span style="color:${newThemeColors.chartTooltipText};">营业额：</span>
                                    <span style="font-weight:bold;margin-left:5px;color:${newThemeColors.primary};">${params[0].value} 元</span>
                                </div>`;
                    };
                    window.saleChart.setOption(window.saleChartOptions, true);
                }

                // Update user chart options
                if (window.userChartOptions) { // Assuming user_option is stored globally or accessible
                    window.userChartOptions.title.textStyle.color = newThemeColors.chartTitleColor;
                    window.userChartOptions.title.subtextStyle.color = newThemeColors.chartSubtitleColor;
                    window.userChartOptions.legend.textStyle.color = newThemeColors.chartAxisLabelColor;
                    window.userChartOptions.xAxis.axisLabel.color = newThemeColors.chartAxisLabelColor;
                    window.userChartOptions.xAxis.axisLine.lineStyle.color = newThemeColors.chartAxisLineColor;
                    window.userChartOptions.yAxis.nameTextStyle.color = newThemeColors.chartAxisLabelColor;
                    window.userChartOptions.yAxis.axisLabel.color = newThemeColors.chartAxisLabelColor;
                    window.userChartOptions.yAxis.splitLine.lineStyle.color = newThemeColors.chartSplitLineColor;
                    window.userChartOptions.series[0].lineStyle.color = newThemeColors.danger;
                    window.userChartOptions.series[0].itemStyle.color = newThemeColors.danger;
                    window.userChartOptions.series[0].areaStyle.color.colorStops[0].color = hexToRgba(newThemeColors.danger, 0.5);
                    window.userChartOptions.series[0].areaStyle.color.colorStops[1].color = hexToRgba(newThemeColors.danger, 0.2);
                    window.userChartOptions.series[0].areaStyle.color.colorStops[2].color = hexToRgba(newThemeColors.danger, 0.05);
                    window.userChartOptions.tooltip.backgroundColor = newThemeColors.chartTooltipBg;
                    window.userChartOptions.tooltip.borderColor = newThemeColors.chartTooltipBorder;
                    window.userChartOptions.tooltip.textStyle.color = newThemeColors.chartTooltipText;
                    window.userChartOptions.tooltip.formatter = function(params) {
                        return `<div style="font-weight:bold;margin-bottom:5px;color:${newThemeColors.chartTooltipText};">${params[0].name}</div>
                                <div style="display:flex;align-items:center;margin-bottom:3px;">
                                    <span style="display:inline-block;margin-right:8px;width:10px;height:10px;border-radius:50%;background-color:${newThemeColors.danger};"></span>
                                    <span style="color:${newThemeColors.chartTooltipText};">访问量：</span>
                                    <span style="font-weight:bold;margin-left:5px;color:${newThemeColors.danger};">${params[0].value} 人</span>
                                </div>`;
                    };
                    window.userChart.setOption(window.userChartOptions, true);
                }
            }
        }

        // Listen for messages from the parent window
        window.addEventListener('message', function (event) {
            // It's a good practice to check event.origin for security, if you know the expected origin
            // if (event.origin !== 'expected-origin') return;
            if (event.data && event.data.type === 'themeChange') {
                console.log('[stat.html] Received themeChange message:', event.data.theme);
                applyTheme(event.data.theme);
            }
        });

        // Request initial theme status from parent when iframe loads
        if (window.parent) {
            console.log('[stat.html] Requesting initial theme status from parent.');
            window.parent.postMessage({ type: 'requestThemeStatus' }, '*'); // Adjust '*' to specific target origin in production
        }
    });

    // 为所有带有lay-href属性的链接添加点击事件处理
    $(document).ready(function() {
        console.log("[stat.html] 添加链接点击事件处理");
        $('a[lay-href]').on('click', function(e) {
            e.preventDefault();
            var url = $(this).attr('lay-href');
            var layId = $(this).attr('lay-id');
            var title = $(this).attr('title') || $(this).text().trim();

            console.log("[stat.html] 链接被点击:", url, layId, title);

            // 调用父窗口的openTabsPage函数
            if (window.parent && window.parent.openTabsPage) {
                console.log("[stat.html] 调用父窗口的openTabsPage函数");
                window.parent.openTabsPage(url, title, layId);
            } else {
                console.error("[stat.html] 父窗口或openTabsPage函数未找到");
                // 如果无法找到父窗口函数，尝试直接跳转
                window.location.href = url;
            }
        });
    });
</script>
