<?php
/**
 * 测试商家入驻通知功能
 */

// 直接测试HTTP API通知
function testNotificationApi() {
    echo "=== 测试通知API ===\n";
    
    $apiUrl = 'https://www.huohanghang.cn/api/admin_notification/testNotification';
    $params = http_build_query([
        'title' => '🏪 测试0元入驻申请',
        'content' => "商家名称：测试商家\n申请人：测试用户\n联系电话：13800138000\n申请时间：" . date('Y-m-d H:i:s'),
        'type' => 'info',
        'url' => '/admin/shop/entry_list',
        'icon' => 4
    ]);
    
    $fullUrl = $apiUrl . '?' . $params;
    echo "请求URL: {$fullUrl}\n";
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 10,
            'ignore_errors' => true,
            'header' => "User-Agent: Test-Notification/1.0\r\n"
        ]
    ]);
    
    $response = file_get_contents($fullUrl, false, $context);
    
    if ($response !== false) {
        $responseData = json_decode($response, true);
        if ($responseData && isset($responseData['code']) && $responseData['code'] == 1) {
            echo "✅ 通知API测试成功\n";
            echo "响应: " . json_encode($responseData, JSON_UNESCAPED_UNICODE) . "\n";
        } else {
            echo "❌ 通知API响应异常\n";
            echo "响应: {$response}\n";
        }
    } else {
        echo "❌ 通知API请求失败\n";
    }
    echo "\n";
}

// 测试WebSocket连接
function testWebSocketConnection() {
    echo "=== 测试WebSocket连接 ===\n";
    
    $host = '127.0.0.1';
    $port = 20211;
    
    $socket = @socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
    if ($socket === false) {
        echo "❌ 无法创建Socket\n";
        return;
    }
    
    $result = @socket_connect($socket, $host, $port);
    if ($result === false) {
        echo "❌ 无法连接到WebSocket服务器 {$host}:{$port}\n";
        @socket_close($socket);
        return;
    }
    
    echo "✅ WebSocket服务器连接成功\n";
    @socket_close($socket);
    echo "\n";
}

// 检查日志文件
function checkLogFiles() {
    echo "=== 检查日志文件 ===\n";
    
    $logDir = '/www/wwwroot/www.huohanghang.cn/server/runtime/log/202507/';
    $logFile = $logDir . date('d') . '.log';
    
    if (!file_exists($logFile)) {
        echo "❌ 日志文件不存在: {$logFile}\n";
        return;
    }
    
    echo "✅ 日志文件存在: {$logFile}\n";
    
    // 搜索最近的通知相关日志
    $command = "tail -50 {$logFile} | grep -i '入驻申请通知\\|HTTP API通知\\|WebSocket通知'";
    $output = shell_exec($command);
    
    if ($output) {
        echo "📝 最近的通知日志:\n";
        echo $output;
    } else {
        echo "⚠️  未找到通知相关日志\n";
    }
    echo "\n";
}

// 模拟0元入驻申请
function simulateFreeEntry() {
    echo "=== 模拟0元入驻申请 ===\n";
    
    // 这里需要实际的用户token和数据
    echo "⚠️  需要实际的用户登录token才能测试完整流程\n";
    echo "建议通过以下方式测试:\n";
    echo "1. 登录用户账号获取token\n";
    echo "2. 调用 POST /api/shop_entry/freeEntry 接口\n";
    echo "3. 观察管理后台是否收到通知\n";
    echo "\n";
}

// 检查管理后台页面
function checkAdminPage() {
    echo "=== 检查管理后台页面 ===\n";
    
    $adminUrl = 'https://www.huohanghang.cn/admin';
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 10,
            'ignore_errors' => true
        ]
    ]);
    
    $response = @file_get_contents($adminUrl, false, $context);
    
    if ($response !== false) {
        if (strpos($response, 'amazing-notification') !== false) {
            echo "✅ 管理后台包含通知系统代码\n";
        } else {
            echo "⚠️  管理后台可能未包含通知系统代码\n";
        }
        
        if (strpos($response, 'showNotification') !== false) {
            echo "✅ 管理后台包含通知函数\n";
        } else {
            echo "❌ 管理后台缺少通知函数\n";
        }
    } else {
        echo "❌ 无法访问管理后台页面\n";
    }
    echo "\n";
}

// 运行所有测试
echo "开始测试商家入驻通知功能...\n\n";

testNotificationApi();
testWebSocketConnection();
checkLogFiles();
simulateFreeEntry();
checkAdminPage();

echo "测试完成！\n";
echo "\n";

echo "🔧 排查建议:\n";
echo "1. 确保管理后台页面已打开并包含通知系统\n";
echo "2. 检查浏览器控制台是否有JavaScript错误\n";
echo "3. 确认WebSocket连接是否正常建立\n";
echo "4. 查看服务器日志确认通知是否发送\n";
echo "5. 测试直接调用通知API是否有效\n";