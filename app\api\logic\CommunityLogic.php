<?php

namespace app\api\logic;

use app\common\basics\Logic;
use app\common\logic\CommunityArticleLogic as CommonCommunityArticleLogic;
use app\common\enum\{
    CommunityCommentEnum,
    GoodsEnum,
    OrderEnum,
    ShopEnum,
    CommunityArticleEnum,
    CommunityLikeEnum
};
use app\common\model\{community\CommunityExhibitionform,
    goods\Goods,
    order\Order,
    order\OrderGoods,
    shop\Shop,
    user\User,
    community\CommunityArticle,
    community\CommunityArticleImage,
    community\CommunityCategory,
    community\CommunityExhibition,
    community\CommunityComment,
    community\CommunityFollow,
    community\CommunityLike,
    community\CommunityTopic};
use app\common\server\{ConfigServer, JsonServer, UrlServer, WechatService};
use think\facade\Cache;
use think\facade\Db;
use think\facade\Log;

use app\admin\logic\community\CommunityExhibitionformLogic;

class CommunityLogic extends Logic
{

    public static function getGoodsLists($user_id, $params, $page, $size)
    {
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],
            ['status', '=', GoodsEnum::STATUS_SHELVES],
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK],
        ];

        $type = !empty($params['type']) ? $params['type'] : 'all';

        if ('buy' == $type) {
            $condition = [
                ['user_id', '=', $user_id],
                ['order_status', '>=', OrderEnum::ORDER_STATUS_NO_PAID],
            ];
            $order_id = Order::where($condition)->column('id');
            $goods_id = OrderGoods::whereIn('order_id', $order_id)->column('goods_id');
            $where[] = ['id', 'in', $goods_id];
        }

        if (!empty($params['keyword'])) {
            $where[] = ['name', 'like', '%' . $params['keyword'] . '%'];
        }

        $model = new Goods();
        $field = ['id' => 'goods_id', 'image', 'name' => 'goods_name', 'min_price' => 'goods_price', 'shop_id'];
        $goods = $model->field($field)->where($where)->select();
        $count = $model->where($where)->count();

        foreach ($goods as &$item) {
            $item['shop_name'] = $item->shop->name;
        }

        $goods->hidden(['shop']);

        return [
            'list' => $goods->toArray(),
            'page' => $page,
            'size' => $size,
            'count' => $count,
            'more' => is_more($count, $page, $size)
        ];
    }

    public static function getShopLists($user_id, $params, $page, $size)
    {
        $where = [
            ['is_freeze', '=', ShopEnum::SHOP_FREEZE_NORMAL],
            ['del', '=', 0],
            ['is_run', '=', ShopEnum::SHOP_RUN_OPEN],
        ];

        $type = !empty($params['type']) ? $params['type'] : 'all';

        if ('buy' == $type) {
            $condition = [
                ['order_status', '>=', OrderEnum::ORDER_STATUS_NO_PAID],
                ['user_id', '=', $user_id]
            ];
            $shop_id = Order::where($condition)->column('shop_id');
            $where[] = ['id', 'in', $shop_id];
        }

        if (!empty($params['keyword'])) {
            $where[] = ['name', 'like', '%' . $params['keyword'] . '%'];
        }

        $whereRaw = 'expire_time =0 OR expire_time > '. time();

        $field = ['id', 'name', 'logo'];
        $lists = Shop::field($field)->where($where)->whereRaw($whereRaw)->select()->toArray();
        $count = Shop::where($where)->whereRaw($whereRaw)->count();

        return [
            'list' => $lists,
            'page' => $page,
            'size' => $size,
            'count' => $count,
            'more' => is_more($count, $page, $size)
        ];
    }

    public static function getRecommendTopic()
    {
        return CommunityTopic::field(['id', 'name', 'cid', 'image'])
            ->where(['is_show' => 1, 'del' => 0])
            ->order(['sort' => 'desc', 'id' => 'desc'])
            ->limit(3)
            ->select()->toArray();
    }

    public static function getTopicLists($get)
    {
        $where[] = ['t.del', '=', 0];
        $where[] = ['t.is_show', '=', 1];
        if (!empty($get['name'])) {
            $where[] = ['t.name', 'like', '%' . $get['name'] . '%'];
        }

        $model = new CommunityCategory();
        $lists = $model->alias('c')
            ->field(['c.id, c.name'])
            ->with(['topic' => function ($query) use ($where) {
                $query->alias('t')->field(['id', 'cid', 'name', 'image', 'click'])
                    ->where($where)
                    ->order(['sort' => 'desc', 'id' => 'desc']);
            }])
            ->where($where)
            ->join('community_topic t', 't.cid = c.id')
            ->group('c.id')
            ->select()
            ->toArray();

        if (empty($get['name'])) {
            $recommend_topic = (new CommunityTopic())->field(['id', 'cid', 'name', 'image', 'click'])
                ->where(['del' => 0, 'is_show' => 1, 'is_recommend' => 1])
                ->select()
                ->toArray();
            $recommend = ['id' => 0, 'name' => '推荐', 'topic' => $recommend_topic];
            array_unshift($lists, $recommend);
        }

        return $lists;
    }

    public static function getSimilarExhibition($id)
    {
        $exhibition = CommunityExhibition::findOrEmpty($id);
        $where[]=['province_id','=',$exhibition['province_id']];
        $where[]=['city_id','=',$exhibition['city_id']];
        $where[]=['district_id','=',$exhibition['district_id']];
        $where[]=['user_id','=',$exhibition['user_id']];
        $where[]=['site','like','%'.$exhibition['site'].'%'];

        return CommunityExhibition::where(function($query) use ($where) {
                $query->whereOr($where);
        })->where('id', '<>', $id)->column('id');
    }

    public static function getExhibitionList($get,$page, $size)
    {
        $where[] = ['del', '=', 0];
        $where[] = ['is_show', '=', 1];
        if (!empty($get['name'])) {
            $where[] = ['title|content', 'like', '%' . $get['name'] . '%'];
        }

        if (!empty($get['user_id'])) {
            $where[] = ['user_id', '=',$get['user_id']];
        }
        if(isset($get['id']) && !empty($get['id'])){
            if(isset($get['likeness']) && $get['likeness'] != ''){
                $ids=self::getSimilarExhibition($get['id']);
                if(!empty($ids)){
                    $where[] = ['id', 'in',$ids ];
                }
            }
        }
        $sort = ['sort' => 'asc'];
        $model = new CommunityExhibition();

        $lists = $model->with(['images'])
            ->where($where)
            ->page($page, $size)
            ->order($sort)
            ->select()
            ->toArray();
        $count = $model->where($where)->count();
        foreach($lists as  &$value){
            $item['click_nums'] = $value['click_nums'] + 1;
            Db::name('community_exhibition')->where('id', $value['id'])->update(['click_nums' => $item['click_nums']]);
        }


        return [
            'list' => $lists,
            'page' => $page,
            'size' => $size,
            'count' => $count,
            'more' => is_more($count, $page, $size)
        ];
    }

    public static function getExhibitionInfo($get,$user_id = null)
    {
        $where[] = ['id', '=',$get['id']];
        $model = new CommunityExhibition();
        $lists = $model->with(['images'])
            ->where($where)
            ->findOrEmpty()->toArray();
        $lists['user_list']=(new CommunityExhibitionform())->alias('f')->leftJoin('user og', 'og.id = f.user_id')->field('og.avatar,f.*')->where(['exhibition_id'=>['=',$get['id']]])->page(1,20)->select();
        $file_data0 = Cache::get('exhibition_type0_'.$get['id']);
        $file_data1 = Cache::get('exhibition_type1_'.$get['id']);
        if(empty($file_data0)){
            $file_data0 = CommunityExhibitionformLogic::integral(['exhibition_id'=>$get['id'],'type_id'=>0], true);
            $file_data1 = CommunityExhibitionformLogic::integral(['exhibition_id'=>$get['id'],'type_id'=>1], true);
            Cache::set('exhibition_type0_'.$get['id'],$file_data0,3600);
            Cache::set('exhibition_type1_'.$get['id'],$file_data1,3600);
        }
        $lists['excel_file']=[
           'type_0'=>isset($file_data0['url'])?$file_data0['url']:'',
           'type_1'=>isset($file_data1['url'])?$file_data1['url']:'',
        ];
            foreach($lists['user_list'] as  &$value){
                $value['avatar']=UrlServer::getFileUrl($value['avatar']);
            }

        return $lists;
    }

    public static function getCate()
    {
        $lists = CommunityCategory::field(['id', 'name'])
            ->where(['is_show' => 1, 'del' => 0])
            ->order(['sort' => 'desc', 'id' => 'asc'])
            ->select()->toArray();
        return $lists;
    }

    public static function getArticleLists($get, $page, $size, $user_id = null)
    {
        $where[] = ['del', '=', 0];
        $where[] = ['status', '=', CommunityArticleEnum::STATUS_SUCCESS];
        if (!empty($get['cate_id'])) {
            $where[] = ['cate_id', '=', $get['cate_id']];
        }
        if (!empty($get['topic_id'])) {
            $where[] = ['topic_id', '=', $get['topic_id']];
        }
        if (!empty($get['keyword'])) {
            $where[] = ['content', 'like', '%' . trim($get['keyword']) . '%'];
            if (!is_null($user_id)) {
                CommunitySearchRecordLogic::recordKeyword(trim($get['keyword']), $user_id);
            }
        }
        if (isset($get['user_id']) && !empty($get['user_id'])) {
            $where[] = ['user_id', '=', $get['user_id']];
        }

        $sort = ['id' => 'desc'];
        if (!empty($get['sort_hot'])) {
            $sort = ['like' => $get['sort_hot'], 'id' => 'desc'];
        }
        if (!empty($get['sort_new'])) {
            $sort = ['id' => $get['sort_new'], 'like' => 'desc'];
        }
        if (empty($sort)) {
            $sort = ['like' => 'desc', 'id' => 'desc'];
        }

        $model = new CommunityArticle();
        $count = $model->where($where)->count();
        $lists = $model
            ->with(['images','user' => function ($query) {
                $query->field(['id', 'nickname', 'avatar','mobile']);
            }])
            ->where($where)
            ->field(['id', 'wxqrcode','user_id', 'cate_id', 'image','videos','content', 'like', 'create_time','click'])
            ->page($page, $size)
            ->order($sort)
            ->select()
            ->bindAttr('user', ['nickname', 'avatar','mobile'])
            ->hidden(['user'])
            ->toArray();

        $likes_article = [];
        if (!is_null($user_id)) {
            $likes_article = CommunityLike::where([
                'user_id' => $user_id,
                'type' => CommunityLikeEnum::TYPE_ARTICLE
            ])->column('relation_id');
        }

        $now_day = date('Y-m-d', time());
        $start_time = strtotime($now_day . ' 00:00:00');
        $end_time = strtotime($now_day . ' 23:59:59');
        foreach ($lists as &$item) {
            $item['avatar'] = !empty($item['avatar']) ? UrlServer::getFileUrl($item['avatar']) : '';
            $item['is_like'] = in_array($item['id'], $likes_article) ? 1 : 0;
            $item['create_time'] =strtotime ($item['create_time']);
           
            $item['click'] = $item['click'] + 1;
            Db::name('community_article')->where('id', $item['id'])->update(['click' => $item['click']]);
            $is_follow = CommunityFollow::where([
                'user_id' => $user_id,
                'follow_id' => $item['user_id'],
                'status' => 1
            ])->findOrEmpty();
            $item['is_follow'] = !$is_follow->isEmpty() ? 1 : 0;

        }
        $today_add=0;
        if (!is_null($user_id)) {
            $audit_day_size=ConfigServer::get('community', 'audit_day_size', 1);
            $today=Db::name('community_article')->where(['user_id'=>$user_id])->where([['create_time','between',[$start_time,$end_time]]])->buildSql();
            $today_add= $today>=$audit_day_size?1:0;
        }
        $has_new = CommonCommunityArticleLogic::hasNew($user_id);
        
        return [
            'has_new' => $has_new,
            'list' => $lists,
            'today_add' => $today_add,
            'page' => $page,
            'size' => $size,
            'count' => $count,
            'more' => is_more($count, $page, $size)
        ];
    }

    public static function addArticle(int $user_id, array $post): bool
    {
        Db::startTrans();
        try {
            // --- 微信内容安全审核开始 ---
            $wechatService = new WechatService();
            // 1. 文本审核
             $user = Db::name('user_auth')->where('user_id', $user_id)->find();
            if (!empty($post['content'])) {
               
                if(empty($user) || empty($user['openid'])){
                    self::$error = '获取用户信息失败，无法进行内容审核';
                    Db::rollback();
                    return false;
                }
                if (!$wechatService->msgSecCheck($post['content'], $user['openid'])) {
                    self::$error = WechatService::getError() ?: '内容包含敏感信息';
                    Db::rollback();
                    return false;
                }
            }
          
            // 2. 图片审核 (异步)
            $traceIds = [];
            if (!empty($post['image'])) {
                if(empty($user) ||empty($user['openid'])){
                    self::$error = '获取用户信息失败，无法进行内容审核';
                    Db::rollback();
                    return false;
                }
                foreach ($post['image'] as $imageUrl) {
                    $traceId = $wechatService->mediaCheckAsync($imageUrl, $user['openid'], 2, 2);
                    if ($traceId && is_string($traceId)) {
                        $traceIds[] = $traceId;
                    } else {
                        Log::error('微信图片安全检查请求失败: ' . $imageUrl);
                    }
                }
            }
            // --- 微信内容安全审核结束 ---

            // 处理数据
            $data = self::getEditArticleData($user_id, $post);
            
            // 状态统一设置为等待审核
            $data['status'] = CommunityArticleEnum::STATUS_WAIT;
            if (!empty($traceIds)) {
                $data['audit_trace_id'] = implode(',', $traceIds);
            }
            
            // 新增文章信息
            $article = CommunityArticle::create($data);

            if(isset($post['video']) && !empty($post['video'])){
                self::addArticleVideo($post['video'], $article['id']);
            }
            // 新增文章关联图片
            if(isset($post['image']) && !empty($post['image'])){
                self::addArticleImage($post['image'], $article['id']);
            }
            // 更新关联话题文章数量
            if (!empty($post['topic_id'])) {
                CommunityTopic::where(['id' => $post['topic_id']])->inc('article_num')->update();
            }

            // 通知粉丝有新作品 (状态为待审核，可能需要调整通知时机)
            CommonCommunityArticleLogic::noticeFans($user_id, $article['status']);

            // 添加活跃度积分
            try {
                \app\common\logic\UserActivityLogic::addActivityScore(
                    $user_id,
                    \app\common\enum\UserActivityEnum::ACTIVITY_PUBLISH_DEMAND,
                    ['article_id' => $article['id'], 'cate_id' => $data['cate_id'] ?? 0]
                );
            } catch (\Exception $e) {
                trace('用户活跃度检查失败: ' . $e->getMessage(), 'error');
            }

            Db::commit();
            return true;

        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    public static function addReports(int $user_id, array $post): bool
    {
       Db::name('user_reports')->insert([
           'user_id' => $user_id,
           'image' => $post['image']??'',
           'remark' => $post['remark']??'',
           'comm_id' => $post['comm_id']??0
       ]);
            return true;
    }

    public static function editArticle(int $user_id, array $post)
    {
        Db::startTrans();
        try {
            $article = CommunityArticle::where(['id' => $post['id'], 'user_id' => $user_id])->findOrEmpty();
            if ($article->isEmpty()) {
                self::$error = '文章不存在';
                return false;
            }
            if ($article['status'] == CommunityArticleEnum::STATUS_SUCCESS) {
                self::$error = '审核通过的文章不可编辑';
                return false;
            }

            // --- 微信内容安全审核开始 ---
            $wechatService = new WechatService();
            if (!empty($post['content'])) {
                $user = User::find($user_id);
                if(empty($user) || empty($user['mnp_openid'])){
                    self::$error = '获取用户信息失败，无法进行内容审核';
                    return false;
                }
                if (!$wechatService->msgSecCheck($post['content'], $user['mnp_openid'])) {
                    self::$error = WechatService::getError() ?: '内容包含敏感信息';
                    return false;
                }
            }
            $traceIds = [];
            if (!empty($post['image'])) {
                $user = User::find($user_id);
                if(empty($user) || empty($user['mnp_openid'])){
                    self::$error = '获取用户信息失败，无法进行内容审核';
                    return false;
                }
                foreach ($post['image'] as $imageUrl) {
                    $traceId = $wechatService->mediaCheckAsync($imageUrl, $user['mnp_openid'], 2, 3);
                    if ($traceId && is_string($traceId)) {
                        $traceIds[] = $traceId;
                    } else {
                        Log::error('微信图片安全检查请求失败: ' . $imageUrl);
                    }
                }
            }
            // --- 微信内容安全审核结束 ---

            $data = self::getEditArticleData($user_id, $post);
            $data['status'] = CommunityArticleEnum::STATUS_WAIT; // 编辑后重新进入待审核
            if (!empty($traceIds)) {
                $data['audit_trace_id'] = implode(',', $traceIds);
            }

            $article->save($data);
            CommunityArticleImage::where(['article_id' => $post['id']])->delete();
            self::addArticleImage($post['image'], $post['id']);
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    public static function delArticle(int $user_id, $post)
    {
        $article = CommunityArticle::where([
            'id' => $post['id'],
            'user_id' => $user_id,
            'del' => 0
        ])->findOrEmpty();

        if ($article->isEmpty()) {
            self::$error = '文章不存在';
            return false;
        }

        $article->del = 1;
        $article->save();

        return true;
    }

    public static function detail($user_id, $id)
    {
        $field = [
            'ca.id', 'ca.user_id', 'ca.content','ca.status', 'ca.image', 'ca.videos', 'ca.like', 'ca.create_time', 'ca.click', 'ca.wxqrcode',
            'u.nickname', 'u.avatar',
            'ct.name as topic_name'
        ];
        $result = Db::name('community_article')->alias('ca')
            ->leftJoin('user u', 'u.id = ca.user_id')
            ->leftJoin('community_topic ct', 'ct.id = ca.topic_id')
            ->field($field)
            ->where(['ca.id' => $id, 'ca.del' => 0])
            ->find();

        if (empty($result)) return [];

        $result['images'] = Db::name('community_article_image')->where('article_id', $id)->column('image');
        foreach($result['images'] as &$v){
            $v = UrlServer::getFileUrl($v);
        }
         $result['image'] = UrlServer::getFileUrl($result['image']);
        // $result['goods'] = Db::name('community_article_goods')->where('article_id', $id)->select()->toArray();
        // $result['shop'] = Db::name('community_article_shop')->where('article_id', $id)->select()->toArray();
        $result['avatar'] = UrlServer::getFileUrl($result['avatar']);
        $result['is_like'] = CommunityLike::where(['user_id' => $user_id, 'relation_id' => $id, 'type' => CommunityLikeEnum::TYPE_ARTICLE])->count() ? 1 : 0;
        $result['is_follow'] = CommunityFollow::where(['user_id' => $user_id, 'follow_id' => $result['user_id'], 'status' => 1])->count() ? 1 : 0;
        
        Db::name('community_article')->where('id', $id)->inc('click')->update();
        // CommunityTopic::where(['id' => $result['id']])->inc('click')->update();
        $result['audit_remark_desc'] = CommunityArticleEnum::getStatusRemarkDesc($result);

        return $result;
    }

    public static function getEditArticleData(int $user_id, array $post)
    {
        $data = [
            'user_id' => $user_id,
            'content' => $post['content'],
            'cate_id' => $post['cate_id'],
            'image' => !empty($post['image']) ? reset($post['image']) : '',
            'videos' => !empty($post['video']) ? reset($post['video']) : '',
            'goods' => !empty($post['goods']) ? array_unique(array_values($post['goods'])) : '',
            'shop' => !empty($post['shop']) ? array_unique(array_values($post['shop'])) : '',
            'wxqrcode' => !empty($post['wxqrcode']) ? $post['wxqrcode'] : '',
            'topic_id' => 0
        ];

        if (!empty($post['topic_id'])) {
            $topic = CommunityTopic::where(['id' => $post['topic_id'], 'is_show' => 1])->findOrEmpty();
            if ($topic->isEmpty()) {
                throw new \Exception('所选话题不存在');
            }
            $data['cate_id'] = $topic['cid'];
            $data['topic_id'] = $post['topic_id'];
        }

        return $data;
    }

    public static function addArticleImage($image, $article_id)
    {
        if (!empty($image)) {
            $images = [];
            foreach ($image as $item) {
                $images[] = [
                    'article_id' => $article_id,
                    'image' => $item,
                ];
            }
            (new CommunityArticleImage())->saveAll($images);
        }
    }

    public static function addArticleVideo($video, $article_id)
    {
        if (!empty($video)) {
            $videos = [];
            foreach ($video as $item) {
                $videos[] = [
                    'article_id' => $article_id,
                    'videos' => $item,
                ];
            }
            // 假设存在 CommunityArticleVideo 模型
            // (new CommunityArticleVideo())->saveAll($videos);
        }
    }

    public static function followRelation($user_id, $post)
    {
        $follow_id = $post['follow_id'];
        if ($user_id == $follow_id) {
            self::$error = '不能关注自己';
            return false;
        }

        $relation = CommunityFollow::where(['user_id' => $user_id, 'follow_id' => $follow_id])->findOrEmpty();
        if ($relation->isEmpty()) {
            CommunityFollow::create([
                'user_id' => $user_id,
                'follow_id' => $follow_id,
                'status' => 1,
            ]);
            User::where('id', $user_id)->inc('follow_num')->update();
            User::where('id', $follow_id)->inc('fans_num')->update();
        } else {
            $status = $relation['status'] ? 0 : 1;
            $relation->status = $status;
            $relation->save();
            if ($status) {
                User::where('id', $user_id)->inc('follow_num')->update();
                User::where('id', $follow_id)->inc('fans_num')->update();
            } else {
                User::where('id', $user_id)->dec('follow_num')->update();
                User::where('id', $follow_id)->dec('fans_num')->update();
            }
        }
        return true;
    }

    public static function giveLike($user_id, $post)
    {
        $type = $post['type'];
        $relation_id = $post['id'];
        $where = [
            'user_id' => $user_id,
            'relation_id' => $relation_id,
            'type' => $type
        ];

        $relation = CommunityLike::where($where)->findOrEmpty();

        if ($type == CommunityLikeEnum::TYPE_ARTICLE) {
            $model = new CommunityArticle();
        } else {
            $model = new CommunityComment();
        }

        if ($relation->isEmpty()) {
            CommunityLike::create($where);
            $model->where('id', $relation_id)->inc('like')->update();
        } else {
            CommunityLike::where($where)->delete();
            $model->where('id', $relation_id)->dec('like')->update();
        }
        return true;
    }

    public static function getFollowArticle($user_id, $page, $size)
    {
        $follow_ids = CommunityFollow::where(['user_id' => $user_id, 'status' => 1])->column('follow_id');
        if (empty($follow_ids)) {
            return ['list' => [], 'page' => $page, 'size' => $size, 'count' => 0, 'more' => 0];
        }

        $where = [
            ['user_id', 'in', $follow_ids],
            ['del', '=', 0],
            ['status', '=', CommunityArticleEnum::STATUS_SUCCESS]
        ];

        $model = new CommunityArticle();
        $count = $model->where($where)->count();
        $lists = $model->with(['images', 'user' => function ($query) {
                $query->field(['id', 'nickname', 'avatar']);
            }])
            ->where($where)
            ->field(['id', 'user_id', 'content', 'like', 'create_time'])
            ->page($page, $size)
            ->order(['id' => 'desc'])
            ->select()
            ->bindAttr('user', ['nickname', 'avatar'])
            ->hidden(['user'])
            ->toArray();

        $likes_article = CommunityLike::where([
            'user_id' => $user_id,
            'type' => CommunityLikeEnum::TYPE_ARTICLE
        ])->column('relation_id');

        foreach ($lists as &$item) {
            $item['avatar'] = UrlServer::getFileUrl($item['avatar']);
            $item['is_like'] = in_array($item['id'], $likes_article) ? 1 : 0;
        }

        return [
            'list' => $lists,
            'page' => $page,
            'size' => $size,
            'count' => $count,
            'more' => is_more($count, $page, $size)
        ];
    }

    public static function followlist($user_id, $page, $size,$post=[])
    {
        $type = $post['type'] ?? 'follow';
        if ($type == 'follow') {
            $user_ids = CommunityFollow::where(['user_id' => $user_id, 'status' => 1])->column('follow_id');
        } else {
            $user_ids = CommunityFollow::where(['follow_id' => $user_id, 'status' => 1])->column('user_id');
        }

        if (empty($user_ids)) {
            return ['list' => [], 'page' => $page, 'size' => $size, 'count' => 0, 'more' => 0];
        }

        $count = User::whereIn('id', $user_ids)->count();
        $lists = User::field(['id', 'nickname', 'avatar'])
            ->whereIn('id', $user_ids)
            ->page($page, $size)
            ->select()
            ->toArray();

        $follow_ids = CommunityFollow::where(['user_id' => $user_id, 'status' => 1])->column('follow_id');
        foreach ($lists as &$item) {
            $item['avatar'] = UrlServer::getFileUrl($item['avatar']);
            $item['is_follow'] = in_array($item['id'], $follow_ids) ? 1 : 0;
        }

        return [
            'list' => $lists,
            'page' => $page,
            'size' => $size,
            'count' => $count,
            'more' => is_more($count, $page, $size)
        ];
    }

    public static function boardInfo()
    {
       
        #浏览人数变量
        $count=Db::name('chat_relation')->count();
        $count=$count<1999?1999+$count:$count;
        $result = [
            'show_nums' => Db::name('community_article')->sum('click'),
            'buy_nums' => Db::name('community_article')->where('cate_id',1)->count(),
            'want_nums' => Db::name('community_article')->where('cate_id',2)->count(),
            'count' =>  $count
        ];
        return $result;
    }

    static function exhibitionBoardInfo($user_id=0){
        
        $result=[
            'show_nums' => Db::name('community_exhibition')->count(),//展会总数  
            'buy_nums' => Db::name('community_exhibitionform')->where('type_id',0)->count(),//观展人数 
            'want_nums' => Db::name('community_exhibitionform')->where('type_id',1)->count(),// 展商咨询 
            'count' =>  Db::name('community_exhibition')->sum('click_nums') //浏览人数
        ];
        return $result;
    }

    public static function getRelationGoodsOrShop($get, $type)
    {
        $ids = $get['ids'];
        if (empty($ids)) return [];

        if ($type == 'goods') {
            $field = ['id', 'name', 'image', 'min_price as price'];
            $lists = Goods::field($field)->whereIn('id', $ids)->select()->toArray();
        } else {
            $field = ['id', 'name', 'logo as image'];
            $lists = Shop::field($field)->whereIn('id', $ids)->select()->toArray();
        }
        return $lists;
    }

    public static function getWorksLists($user_id, $get, $page, $size)
    {
        $where = [
            ['user_id', '=', $get['user_id']],
            ['del', '=', 0]
        ];
        if ($user_id != $get['user_id']) {
            $where[] = ['status', '=', CommunityArticleEnum::STATUS_SUCCESS];
        }

        $model = new CommunityArticle();
        $count = $model->where($where)->count();
        $lists = $model->with(['images', 'user' => function ($query) {
                $query->field(['id', 'nickname', 'avatar']);
            }])
            ->where($where)
            ->field(['id', 'user_id', 'content', 'like', 'create_time', 'status'])
            ->page($page, $size)
            ->order(['id' => 'desc'])
            ->select()
            ->bindAttr('user', ['nickname', 'avatar'])
            ->hidden(['user'])
            ->toArray();

        $likes_article = CommunityLike::where([
            'user_id' => $user_id,
            'type' => CommunityLikeEnum::TYPE_ARTICLE
        ])->column('relation_id');

        foreach ($lists as &$item) {
            $item['avatar'] = UrlServer::getFileUrl($item['avatar']);
            $item['is_like'] = in_array($item['id'], $likes_article) ? 1 : 0;
            $item['status_desc'] = CommunityArticleEnum::getDesc($item['status']);
        }

        return [
            'list' => $lists,
            'page' => $page,
            'size' => $size,
            'count' => $count,
            'more' => is_more($count, $page, $size)
        ];
    }

    public static function getLikeLists($user_id, $get, $page, $size)
    {
        $where = [
            ['cl.user_id', '=', $user_id],
            ['ca.del', '=', 0],
            ['ca.status', '=', CommunityArticleEnum::STATUS_SUCCESS]
        ];

        $count = Db::name('community_like')->alias('cl')->where($where)->join('community_article ca', 'ca.id = cl.relation_id')->count();
        $lists = Db::name('community_like')->alias('cl')
            ->join('community_article ca', 'ca.id = cl.relation_id')
            ->join('user u', 'u.id = ca.user_id')
            ->field(['ca.id', 'ca.user_id', 'ca.content', 'ca.like', 'ca.create_time', 'u.nickname', 'u.avatar'])
            ->where($where)
            ->page($page, $size)
            ->order(['cl.id' => 'desc'])
            ->select()
            ->toArray();

        $likes_article = CommunityLike::where([
            'user_id' => $user_id,
            'type' => CommunityLikeEnum::TYPE_ARTICLE
        ])->column('relation_id');

        foreach ($lists as &$item) {
            $item['avatar'] = UrlServer::getFileUrl($item['avatar']);
            $item['is_like'] = in_array($item['id'], $likes_article) ? 1 : 0;
        }

        return [
            'list' => $lists,
            'page' => $page,
            'size' => $size,
            'count' => $count,
            'more' => is_more($count, $page, $size)
        ];
    }

    public static function getTopicArticle($get, $page, $size)
    {
        $topic = CommunityTopic::where(['id' => $get['topic_id'], 'del' => 0])->findOrEmpty();
        if ($topic->isEmpty()) {
            self::$error = '话题不存在';
            return false;
        }
        $topic['article_num'] = CommunityArticle::where(['topic_id' => $get['topic_id'], 'del' => 0])->count();
        $topic['lists'] = self::getArticleLists($get, $page, $size);
        return $topic->toArray();
    }

    public static function addExhibitionForm(int $user_id, array $post): bool
    {
        Db::startTrans();
        try {
            $data = [
                'user_id' => $user_id,
                'name' => $post['name'],
                'mobile' => $post['mobile'],
                'exhibition_id' => $post['exhibition_id'],
                'type_id' => $post['type_id'],
                'create_time' => time(),
            ];
            (new CommunityExhibitionform())->save($data);
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }
}