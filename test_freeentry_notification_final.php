<?php
/**
 * 测试0元入驻通知功能 - 最终版本
 */

echo "=== 测试0元入驻通知功能 - 最终版本 ===\n\n";

// 测试1: 检查WebSocket服务器状态
echo "1. 检查WebSocket服务器状态...\n";
$wsHost = '127.0.0.1';
$wsPort = 20211;
$connection = @fsockopen($wsHost, $wsPort, $errno, $errstr, 5);

if ($connection) {
    echo "✅ WebSocket服务器正在运行 (端口: {$wsPort})\n";
    fclose($connection);
} else {
    echo "❌ WebSocket服务器未运行或端口不可访问\n";
    echo "错误: {$errstr} ({$errno})\n";
}
echo "\n";

// 测试2: 测试WebSocket通知发送
echo "2. 测试WebSocket通知发送...\n";
function testWebSocketNotification($title, $content, $type = 'info') {
    try {
        $wsHost = '127.0.0.1';
        $wsPort = 20211;
        
        // 构建通知数据
        $notification = [
            'event' => 'admin_notification',
            'data' => [
                'type' => $type . '_notification',
                'title' => $title,
                'content' => $content,
                'url' => '/admin/shop/entry',
                'icon' => 4,
                'timestamp' => time()
            ]
        ];
        
        // 创建socket连接
        $socket = @socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
        if (!$socket) {
            throw new Exception('无法创建socket');
        }
        
        // 设置超时
        socket_set_option($socket, SOL_SOCKET, SO_RCVTIMEO, ['sec' => 3, 'usec' => 0]);
        socket_set_option($socket, SOL_SOCKET, SO_SNDTIMEO, ['sec' => 3, 'usec' => 0]);
        
        // 连接到WebSocket服务器
        $result = @socket_connect($socket, $wsHost, $wsPort);
        if (!$result) {
            throw new Exception('无法连接到WebSocket服务器');
        }
        
        // 构建WebSocket握手请求
        $key = base64_encode(random_bytes(16));
        $headers = [
            "GET /?admin_id=1&nickname=测试系统&token=test_token&type=admin&client=5 HTTP/1.1",
            "Host: {$wsHost}:{$wsPort}",
            "Upgrade: websocket",
            "Connection: Upgrade",
            "Sec-WebSocket-Key: {$key}",
            "Sec-WebSocket-Version: 13",
            "",
            ""
        ];
        
        $handshake = implode("\r\n", $headers);
        @socket_write($socket, $handshake, strlen($handshake));
        
        // 读取握手响应
        $response = @socket_read($socket, 1024);
        if (!$response || strpos($response, '101 Switching Protocols') === false) {
            throw new Exception('WebSocket握手失败');
        }
        
        // 创建WebSocket数据帧
        $message = json_encode($notification);
        $frame = createWebSocketFrame($message);
        @socket_write($socket, $frame, strlen($frame));
        
        // 关闭连接
        @socket_close($socket);
        
        return true;
    } catch (Exception $e) {
        if (isset($socket)) {
            @socket_close($socket);
        }
        echo "WebSocket发送失败: " . $e->getMessage() . "\n";
        return false;
    }
}

// 创建WebSocket数据帧的函数
function createWebSocketFrame($message) {
    $length = strlen($message);
    $frame = chr(0x81); // FIN=1, opcode=1 (text frame)
    
    if ($length < 126) {
        $frame .= chr($length | 0x80); // MASK=1
    } elseif ($length < 65536) {
        $frame .= chr(126 | 0x80) . pack('n', $length);
    } else {
        $frame .= chr(127 | 0x80) . pack('J', $length);
    }
    
    // 生成掩码
    $mask = random_bytes(4);
    $frame .= $mask;
    
    // 应用掩码
    for ($i = 0; $i < $length; $i++) {
        $frame .= $message[$i] ^ $mask[$i % 4];
    }
    
    return $frame;
}

// 发送测试通知
$result = testWebSocketNotification(
    '🏪 测试0元入驻申请',
    "用户：测试用户\n申请时间：" . date('Y-m-d H:i:s') . "\n请及时审核处理。",
    'info'
);

if ($result) {
    echo "✅ WebSocket通知发送成功\n";
} else {
    echo "❌ WebSocket通知发送失败\n";
}
echo "\n";

// 测试3: 测试HTTP API通知
echo "3. 测试HTTP API通知...\n";
$apiUrl = 'https://www.huohanghang.cn/api/admin_notification/testNotification';
$params = http_build_query([
    'title' => '🏪 HTTP测试0元入驻申请',
    'content' => "用户：HTTP测试用户\n申请时间：" . date('Y-m-d H:i:s') . "\n请及时审核处理。",
    'type' => 'info',
    'icon' => 4
]);

$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'timeout' => 10,
        'ignore_errors' => true
    ]
]);

$response = @file_get_contents($apiUrl . '?' . $params, false, $context);
if ($response !== false) {
    $data = json_decode($response, true);
    if ($data && $data['code'] == 1) {
        echo "✅ HTTP API通知发送成功\n";
        echo "响应: " . json_encode($data, JSON_UNESCAPED_UNICODE) . "\n";
    } else {
        echo "❌ HTTP API通知发送失败\n";
        echo "响应: {$response}\n";
    }
} else {
    echo "❌ HTTP API请求失败\n";
}
echo "\n";

// 测试4: 检查日志文件
echo "4. 检查最新的日志记录...\n";
$logFile = '/www/wwwroot/www.huohanghang.cn/server/runtime/log/202507/' . date('d') . '.log';

if (file_exists($logFile)) {
    echo "✅ 日志文件存在: {$logFile}\n";
    
    // 获取最后20行日志
    $command = "tail -20 {$logFile}";
    $output = shell_exec($command);
    
    // 搜索通知相关的日志
    $lines = explode("\n", $output);
    $notificationLogs = [];
    
    foreach ($lines as $line) {
        if (strpos($line, '0元入驻申请通知发送') !== false || 
            strpos($line, 'WebSocket通知发送') !== false ||
            strpos($line, 'HTTP通知发送') !== false ||
            strpos($line, 'admin_notification') !== false) {
            $notificationLogs[] = $line;
        }
    }
    
    if (!empty($notificationLogs)) {
        echo "📝 找到 " . count($notificationLogs) . " 条相关日志:\n";
        foreach (array_slice($notificationLogs, -3) as $log) {
            echo "  " . trim($log) . "\n";
        }
    } else {
        echo "⚠️  未找到相关日志记录\n";
    }
} else {
    echo "❌ 日志文件不存在\n";
}
echo "\n";

echo "=== 测试完成 ===\n";
echo "\n";

echo "📖 使用说明:\n";
echo "1. 确保WebSocket服务器正在运行\n";
echo "2. 打开管理后台页面: https://www.huohanghang.cn/admin\n";
echo "3. 确保管理后台页面包含通知系统代码\n";
echo "4. 在另一个标签页测试0元入驻申请\n";
echo "5. 观察管理后台是否收到通知\n";
echo "\n";

echo "🔧 如果仍然收不到通知，请检查:\n";
echo "1. 管理后台页面是否正确加载了通知系统JavaScript\n";
echo "2. 浏览器控制台是否有JavaScript错误\n";
echo "3. WebSocket连接是否正常建立\n";
echo "4. 服务器日志中是否有通知发送记录\n";
echo "5. 管理员是否正确设置了ADMIN_ID等变量\n";