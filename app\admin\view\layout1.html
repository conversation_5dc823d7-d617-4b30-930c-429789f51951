<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{:url()}</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="__PUBLIC__/static/lib/layui/css/layui.css?v={$front_version}">
    <link rel="stylesheet" href="__PUBLIC__/static/admin/css/app.css?v={$front_version}">
    <link rel="stylesheet" href="__PUBLIC__/static/admin/css/like.css?v={$front_version}">
    
    <!-- 惊艳通知弹窗样式 -->
    <style>
        /* 通知容器 */
        .amazing-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 380px;
            max-width: 90vw;
            z-index: 99999;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 通知卡片 */
        .notification-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 0;
            margin-bottom: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.1);
            overflow: hidden;
            position: relative;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.2);
            transform: translateX(400px);
            opacity: 0;
            animation: slideInRight 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
        }

        /* 不同类型的渐变背景 */
        .notification-card.system {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .notification-card.success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }
        .notification-card.error {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        }
        .notification-card.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .notification-card.info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        /* 光效背景 */
        .notification-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        /* 头部区域 */
        .notification-header {
            padding: 20px 20px 15px;
            position: relative;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        /* 图标容器 */
        .notification-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            animation: pulse 2s infinite;
            position: relative;
            overflow: hidden;
        }

        .notification-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(transparent, rgba(255,255,255,0.3), transparent);
            animation: rotate 3s linear infinite;
        }

        /* 标题和内容 */
        .notification-content {
            flex: 1;
            color: white;
        }

        .notification-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0 0 8px 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            animation: fadeInUp 0.8s ease-out 0.2s both;
        }

        .notification-message {
            font-size: 14px;
            line-height: 1.5;
            opacity: 0.95;
            margin: 0;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
            animation: fadeInUp 0.8s ease-out 0.4s both;
        }

        /* 关闭按钮 */
        .notification-close {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .notification-close:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }

        /* 进度条 */
        .notification-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 4px;
            background: rgba(255,255,255,0.3);
            width: 100%;
            animation: progressBar 5s linear forwards;
        }

        /* 粒子效果容器 */
        .notification-particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255,255,255,0.6);
            border-radius: 50%;
            animation: float 3s infinite ease-in-out;
        }

        /* 动画定义 */
        @keyframes slideInRight {
            0% {
                transform: translateX(400px);
                opacity: 0;
            }
            100% {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            0% {
                transform: translateX(0);
                opacity: 1;
            }
            100% {
                transform: translateX(400px);
                opacity: 0;
            }
        }

        @keyframes fadeInUp {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(255,255,255,0.4);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 0 0 10px rgba(255,255,255,0);
            }
        }

        @keyframes shimmer {
            0% {
                left: -100%;
            }
            100% {
                left: 100%;
            }
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        @keyframes progressBar {
            0% {
                width: 100%;
            }
            100% {
                width: 0%;
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 1;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 0.5;
            }
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .amazing-notification {
                top: 10px;
                right: 10px;
                left: 10px;
                width: auto;
            }
            
            .notification-card {
                transform: translateY(-100px);
                animation: slideInDown 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
            }
        }

        @keyframes slideInDown {
            0% {
                transform: translateY(-100px);
                opacity: 0;
            }
            100% {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* 震动效果 */
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        .notification-card.shake {
            animation: shake 0.5s ease-in-out;
        }

        /* 发光效果 */
        .notification-card.glow {
            box-shadow: 0 0 30px rgba(255,255,255,0.5), 0 20px 40px rgba(0,0,0,0.15);
        }
    </style>
    <script src="__PUBLIC__/static/lib/layui/layui.js?v={$front_version}"></script>
    <script src="__PUBLIC__/static/admin/js/app.js"></script>
    <script src="__PUBLIC__/static/admin/js/notification.js?v={$front_version}"></script>

    <!-- WebSocket连接检测脚本 -->
    <script>
        // 不再自动重定向到HTTP版本
        // 而是在WebSocket连接失败时提供帮助信息
        window.wsConnectionFailed = false;

        // 添加WebSocket连接失败处理函数
        window.handleWSConnectionFailure = function(error) {
            if (window.wsConnectionFailed) return; // 避免多次显示

            window.wsConnectionFailed = true;
            console.error('WebSocket连接失败:', error);

            // 如果是HTTPS页面尝试连接WS协议导致的安全错误
            if (window.location.protocol === 'https:' &&
                error && error.message &&
                error.message.indexOf('insecure WebSocket connection') > -1) {

                // 显示帮助信息
                var helpMessage = '检测到WebSocket连接安全错误。\n\n' +
                    '原因：您正通过HTTPS访问，但WebSocket服务器未配置SSL证书。\n\n' +
                    '解决方案：\n' +
                    '1. 请联系管理员为WebSocket服务器配置SSL证书\n' +
                    '2. 或临时使用HTTP访问管理后台\n\n' +
                    '是否切换到HTTP版本？';

                if (confirm(helpMessage)) {
                    // 用户选择切换到HTTP版本
                    var httpUrl = window.location.href.replace('https://', 'http://');
                    window.location.href = httpUrl;
                }
            }
        };
    </script>
</head>

<body>
{$js_code|raw}
<script src="__PUBLIC__/static/admin/js/jquery.min.js"></script>
<script src="__PUBLIC__/static/admin/js/function.js"></script>

{__CONTENT__}
</body>
</html>

<!-- 将SSE相关代码移到页面底部，确保DOM完全加载后执行 -->
<script>
    // 确保DOM完全加载后执行
    $(document).ready(function(){
        layui.use(['layer', 'jquery'], function(){
            var layer = layui.layer;
            var $ = layui.jquery;
            var notificationSoundUrl = '/uploads/audio/tomsg.mp3';
            var debugMode = true;
            var ws;
            var wsUrl = typeof webSocketUrl !== 'undefined' ? webSocketUrl : (window.location.protocol === 'https:' ? 'wss://' : 'ws://') + 'kefu.huohanghang.cn';

            var adminId = typeof ADMIN_ID !== 'undefined' ? ADMIN_ID : 0;
            var adminNickname = typeof ADMIN_NICKNAME !== 'undefined' ? ADMIN_NICKNAME : 'Admin';
            var adminToken = typeof ADMIN_TOKEN !== 'undefined' ? ADMIN_TOKEN : ''; // Auth token if needed by your WS

            function connectWebSocket() {
                if (!adminId) {
                    // console.warn("Admin ID not available, WebSocket connection aborted.");
                    // if(debugMode) layer.msg('Admin ID not found, cannot connect to notifications.', {icon: 2});
                    return;
                }

                var fullWsUrl = wsUrl + '?client=5&type=admin&admin_id=' + adminId + '&nickname=' + encodeURIComponent(adminNickname) + '&token=' + adminToken;
                if(debugMode) console.log('Connecting to WebSocket:', fullWsUrl);

                ws = new WebSocket(fullWsUrl);

                ws.onopen = function() {
                    if(debugMode) console.log('WebSocket connection established for admin notifications.');
                    // Optional: send a ping or login confirmation if your server expects it beyond URL params
                    // ws.send(JSON.stringify({ event: 'ping', data: { timestamp: Date.now() } }));
                };

                ws.onmessage = function(event) {
                    if(debugMode) console.log('WebSocket message received:', event.data);
                    try {
                        var response = JSON.parse(event.data);
                        var eventType = response.event;
                        var messageData = response.data;

                        if (eventType === 'notification' || eventType === 'admin_notification') {
                            if (messageData && messageData.title && messageData.content) {
                                showNotification(
                                    messageData.title,
                                    messageData.content,
                                    messageData.type || 'admin_notification', // 'type' here is the notification_type from backend
                                    messageData.url || '',
                                    messageData.icon // This was 'icon' in NotificationService payload
                                );
                            }
                        } else if (eventType === 'login' && messageData.msg) {
                            //  if(debugMode) layer.msg(messageData.msg, {icon:1, time: 2000});
                        } else if (eventType === 'error' && messageData.msg) {
                            layer.msg('Notification Error: ' + messageData.msg, {icon: 2});
                        } else if (eventType === 'pong') {
                            if(debugMode) console.log('Pong received from server.');
                        }
                    } catch (e) {
                        if(debugMode) console.error('Error processing WebSocket message:', e);
                        // showNotification('Raw Message', event.data, 'raw_ws_message');
                    }
                };

                ws.onerror = function(error) {
                    if(debugMode) console.error('WebSocket Error:', error);
                    // window.handleWSConnectionFailure(error); // Use existing handler if desired
                    // layer.msg('Notification service connection error.', {icon: 2});
                };

                ws.onclose = function(event) {
                    if(debugMode) console.log('WebSocket connection closed. Code:', event.code, 'Reason:', event.reason);
                    // Optional: attempt to reconnect after a delay
                    // setTimeout(connectWebSocket, 5000);
                };
            }

            // Initialize WebSocket connection
            connectWebSocket();

            // Heartbeat: send ping every 15 seconds (as per swoole.php config)
            setInterval(function() {
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({ event: 'ping', data: { timestamp: Date.now() } }));
                }
            }, 15000);


            // --- Notification Display Logic (from existing SSE, adapted) ---
            window.muteNotificationSound = true; // Keep sound muted initially
            $(document).on('click', function() { // Enable sound on click
                if (window.muteNotificationSound) {
                    enableNotificationSound();
                }
            });
            
            // 惊艳通知弹窗函数
            function showNotification(title, content, type, url, icon) {
                if (debugMode) {
                    console.log('Showing amazing notification:', { title: title, content: content, type: type, url: url, icon: icon });
                }

                // 播放通知声音
                playNotificationSound();

                // 确保通知容器存在
                var container = document.querySelector('.amazing-notification');
                if (!container) {
                    container = document.createElement('div');
                    container.className = 'amazing-notification';
                    document.body.appendChild(container);
                }

                // 确定通知类型和图标
                var notificationType = 'system';
                var iconSymbol = '🔔';
                
                if (type === 'success_notification' || icon === 1) {
                    notificationType = 'success';
                    iconSymbol = '✅';
                } else if (type === 'error_notification' || icon === 2) {
                    notificationType = 'error';
                    iconSymbol = '❌';
                } else if (type === 'warning_notification' || icon === 3) {
                    notificationType = 'warning';
                    iconSymbol = '⚠️';
                } else if (type === 'info_notification' || icon === 4) {
                    notificationType = 'info';
                    iconSymbol = '💡';
                }

                // 创建通知卡片
                var notificationId = 'notification-' + Date.now();
                var cardHtml = `
                    <div class="notification-card ${notificationType}" id="${notificationId}">
                        <!-- 光效背景 -->
                        <div class="notification-particles">
                            ${generateParticles()}
                        </div>
                        
                        <!-- 头部内容 -->
                        <div class="notification-header">
                            <div class="notification-icon">
                                <span style="position: relative; z-index: 2;">${iconSymbol}</span>
                            </div>
                            <div class="notification-content">
                                <h4 class="notification-title">${title || '系统通知'}</h4>
                                <p class="notification-message">${content || '您有一条新的消息'}</p>
                            </div>
                        </div>
                        
                        <!-- 关闭按钮 -->
                        <button class="notification-close" onclick="closeNotification('${notificationId}')">
                            ×
                        </button>
                        
                        <!-- 进度条 -->
                        <div class="notification-progress"></div>
                    </div>
                `;

                // 添加到容器
                container.insertAdjacentHTML('beforeend', cardHtml);
                
                // 获取刚创建的通知元素
                var notificationElement = document.getElementById(notificationId);
                
                // 添加点击事件（如果有URL）
                if (url) {
                    notificationElement.style.cursor = 'pointer';
                    notificationElement.addEventListener('click', function(e) {
                        if (!e.target.classList.contains('notification-close')) {
                            window.open(url, '_blank');
                            closeNotification(notificationId);
                        }
                    });
                }

                // 添加震动效果（错误通知）
                if (notificationType === 'error') {
                    setTimeout(function() {
                        notificationElement.classList.add('shake');
                    }, 100);
                }

                // 添加发光效果（成功通知）
                if (notificationType === 'success') {
                    setTimeout(function() {
                        notificationElement.classList.add('glow');
                    }, 200);
                }

                // 自动关闭
                setTimeout(function() {
                    closeNotification(notificationId);
                }, 8000);

                return notificationId;
            }

            // 生成粒子效果
            function generateParticles() {
                var particles = '';
                for (var i = 0; i < 8; i++) {
                    var left = Math.random() * 100;
                    var delay = Math.random() * 3;
                    var duration = 2 + Math.random() * 2;
                    particles += `<div class="particle" style="left: ${left}%; animation-delay: ${delay}s; animation-duration: ${duration}s;"></div>`;
                }
                return particles;
            }

            // 关闭通知函数
            window.closeNotification = function(notificationId) {
                var notification = document.getElementById(notificationId);
                if (notification) {
                    // 添加退出动画
                    notification.style.animation = 'slideOutRight 0.4s ease-in-out forwards';
                    
                    // 动画完成后移除元素
                    setTimeout(function() {
                        if (notification && notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 400);
                }
            };

            // 清除所有通知
            window.clearAllNotifications = function() {
                var container = document.querySelector('.amazing-notification');
                if (container) {
                    var notifications = container.querySelectorAll('.notification-card');
                    notifications.forEach(function(notification) {
                        notification.style.animation = 'slideOutRight 0.4s ease-in-out forwards';
                    });
                    
                    setTimeout(function() {
                        if (container) {
                            container.innerHTML = '';
                        }
                    }, 400);
                }
            };

            var audioContext;
            var audioBuffer;

            function initAudioContext() {
                try {
                    window.AudioContext = window.AudioContext || window.webkitAudioContext;
                    if (window.AudioContext) {
                        audioContext = new AudioContext();
                        // Unlock audio context on first user gesture
                        var unlockAudio = function() {
                            if (audioContext.state === 'suspended') {
                                audioContext.resume();
                            }
                            document.body.removeEventListener('click', unlockAudio);
                            document.body.removeEventListener('touchend', unlockAudio);
                        };
                        document.body.addEventListener('click', unlockAudio, false);
                        document.body.addEventListener('touchend', unlockAudio, false);

                        // Load sound
                        var request = new XMLHttpRequest();
                        request.open('GET', notificationSoundUrl, true);
                        request.responseType = 'arraybuffer';
                        request.onload = function() {
                            audioContext.decodeAudioData(request.response, function(buffer) {
                                audioBuffer = buffer;
                            }, function(e){ console.error("Error with decoding audio data " + e.err); });
                        }
                        request.send();
                    }
                } catch(e) {
                    console.warn('Web Audio API is not supported in this browser or notification sound could not be initialized.');
                }
            }
            initAudioContext();


            function playNotificationSound() {
                if (!window.muteNotificationSound && audioBuffer && audioContext && audioContext.state === 'running') {
                    var source = audioContext.createBufferSource();
                    source.buffer = audioBuffer;
                    source.connect(audioContext.destination);
                    source.start(0);
                } else if (window.muteNotificationSound && debugMode) {
                    console.log("Notification sound is muted or audio context not ready.");
                }
            }

            function enableNotificationSound() {
                window.muteNotificationSound = false;
                if (audioContext && audioContext.state === 'suspended') {
                    audioContext.resume();
                }
                if(debugMode) console.log('Notification sound enabled.');
                layer.msg('通知声音已开启', {icon:1, time:1500});
            }

            function disableNotificationSound() {
                window.muteNotificationSound = true;
                if(debugMode) console.log('Notification sound disabled.');
                layer.msg('通知声音已关闭', {icon:1, time:1500});
            }
            
            // Make functions globally available if needed by other scripts or iframes
            window.showNotification = showNotification;
            window.enableNotificationSound = enableNotificationSound;
            window.disableNotificationSound = disableNotificationSound;
            window.playNotificationSound = playNotificationSound;
        }); // 结束 layui.use
    }); // 结束 $(document).ready
    </script>
