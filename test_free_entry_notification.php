<?php
/**
 * 测试0元入驻申请通知功能
 */

echo "=== 测试0元入驻申请通知功能 ===\n\n";

// 测试1: 直接测试通知API
echo "1. 测试通知API是否正常...\n";
$apiUrl = 'https://www.huohanghang.cn/api/admin_notification/testNotification';
$params = http_build_query([
    'title' => '🏪 测试0元入驻申请',
    'content' => "商家名称：测试商家\n申请人：测试用户\n联系电话：13800138000\n申请时间：" . date('Y-m-d H:i:s'),
    'type' => 'info',
    'icon' => 4
]);

$response = file_get_contents($apiUrl . '?' . $params);
$data = json_decode($response, true);

if ($data && $data['code'] == 1) {
    echo "✅ 通知API测试成功\n";
    echo "响应: " . json_encode($data, JSON_UNESCAPED_UNICODE) . "\n";
} else {
    echo "❌ 通知API测试失败\n";
    echo "响应: {$response}\n";
}
echo "\n";

// 测试2: 检查日志文件
echo "2. 检查最新的日志记录...\n";
$logFile = '/www/wwwroot/www.huohanghang.cn/server/runtime/log/202507/' . date('d') . '.log';

if (file_exists($logFile)) {
    echo "✅ 日志文件存在: {$logFile}\n";
    
    // 获取最后50行日志
    $command = "tail -50 {$logFile}";
    $output = shell_exec($command);
    
    // 搜索通知相关的日志
    $lines = explode("\n", $output);
    $notificationLogs = [];
    
    foreach ($lines as $line) {
        if (strpos($line, 'freeEntry') !== false || 
            strpos($line, '入驻申请通知') !== false ||
            strpos($line, 'sendAdminNotification') !== false ||
            strpos($line, 'HTTP通知') !== false) {
            $notificationLogs[] = $line;
        }
    }
    
    if (!empty($notificationLogs)) {
        echo "📝 找到 " . count($notificationLogs) . " 条相关日志:\n";
        foreach (array_slice($notificationLogs, -5) as $log) {
            echo "  " . trim($log) . "\n";
        }
    } else {
        echo "⚠️  未找到相关日志记录\n";
    }
} else {
    echo "❌ 日志文件不存在\n";
}
echo "\n";

// 测试3: 检查管理后台页面
echo "3. 检查管理后台通知系统...\n";
$testPageUrl = 'https://www.huohanghang.cn/test-admin-notification.html';
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'timeout' => 10,
        'ignore_errors' => true
    ]
]);

$response = @file_get_contents($testPageUrl, false, $context);
if ($response !== false) {
    if (strpos($response, 'amazing-notification') !== false) {
        echo "✅ 测试页面包含通知系统\n";
    } else {
        echo "❌ 测试页面缺少通知系统\n";
    }
    
    if (strpos($response, 'showAmazingNotification') !== false) {
        echo "✅ 测试页面包含通知函数\n";
    } else {
        echo "❌ 测试页面缺少通知函数\n";
    }
} else {
    echo "❌ 无法访问测试页面\n";
}
echo "\n";

// 测试4: 模拟调用freeEntry接口（需要登录）
echo "4. 模拟freeEntry接口调用...\n";
echo "⚠️  由于需要用户登录，无法直接测试freeEntry接口\n";
echo "建议手动测试步骤:\n";
echo "1. 登录用户账号\n";
echo "2. 调用 POST /api/shop_entry/freeEntry 接口\n";
echo "3. 观察管理后台是否收到通知\n";
echo "4. 检查日志文件中的通知记录\n";
echo "\n";

// 测试5: 检查WebSocket服务器状态
echo "5. 检查WebSocket服务器状态...\n";
$wsPort = 20211;
$connection = @fsockopen('127.0.0.1', $wsPort, $errno, $errstr, 5);

if ($connection) {
    echo "✅ WebSocket服务器正在运行 (端口: {$wsPort})\n";
    fclose($connection);
} else {
    echo "❌ WebSocket服务器未运行或端口不可访问\n";
    echo "错误: {$errstr} ({$errno})\n";
}
echo "\n";

echo "=== 测试完成 ===\n";
echo "\n";

echo "📖 使用说明:\n";
echo "1. 访问测试页面: https://www.huohanghang.cn/test-admin-notification.html\n";
echo "2. 点击各种通知按钮测试效果\n";
echo "3. 登录管理后台: https://www.huohanghang.cn/admin\n";
echo "4. 保持管理后台页面打开\n";
echo "5. 在另一个标签页提交0元入驻申请\n";
echo "6. 观察管理后台是否收到通知\n";
echo "\n";

echo "🔧 如果仍然收不到通知，请检查:\n";
echo "1. 管理后台页面是否包含通知系统代码\n";
echo "2. 浏览器控制台是否有JavaScript错误\n";
echo "3. WebSocket连接是否正常建立\n";
echo "4. 服务器日志中是否有通知发送记录\n";
echo "5. 用户是否成功登录并有权限调用freeEntry接口\n";