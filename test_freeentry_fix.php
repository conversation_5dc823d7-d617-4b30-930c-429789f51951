<?php
/**
 * 测试 freeEntry 接口修复
 */

// 模拟请求数据
$testData = [
    'name' => '测试商家',
    'mobile' => '13800138000',
    'nickname' => '测试用户'
];

echo "测试数据：\n";
echo json_encode($testData, JSON_UNESCAPED_UNICODE) . "\n\n";

// 测试 API 调用
$apiUrl = 'http://localhost/api/shop_entry/freeEntry';

$postData = http_build_query($testData);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => "Content-Type: application/x-www-form-urlencoded\r\n" .
                   "Content-Length: " . strlen($postData) . "\r\n",
        'content' => $postData,
        'timeout' => 30
    ]
]);

echo "正在调用 API: {$apiUrl}\n";
echo "POST 数据: {$postData}\n\n";

$response = @file_get_contents($apiUrl, false, $context);

if ($response === false) {
    echo "API 调用失败\n";
    $error = error_get_last();
    if ($error) {
        echo "错误信息: " . $error['message'] . "\n";
    }
} else {
    echo "API 响应:\n";
    echo $response . "\n\n";
    
    $responseData = json_decode($response, true);
    if ($responseData) {
        echo "解析后的响应:\n";
        echo json_encode($responseData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
    }
}

echo "\n测试完成\n";
?>