{"mcpServers": {"fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "env": {}, "disabled": true, "autoApprove": []}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "disabled": false, "autoApprove": []}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "disabled": false, "autoApprove": []}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "disabled": false, "autoApprove": []}, "fs-mcp-server": {"command": "npx", "args": ["-y", "@bunas/fs-mcp@latest", "--API_KEY=asdqwezxc"], "disabled": false, "autoApprove": []}, "browser-tools-mcp": {"command": "npx", "args": ["@agentdeskai/browser-tools-mcp@latest"], "disabled": false, "autoApprove": []}, "edgeone-pages-mcp-server": {"command": "npx", "args": ["edgeone-pages-mcp"], "disabled": false, "autoApprove": []}, "knowledge-graph-memory-server": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "disabled": false, "autoApprove": []}, "MySQL Server": {"command": "node", "args": ["mysql_mcp_server/index.js", "--host=rm-bp1y50i48aa95aw2beo.mysql.rds.aliyuncs.com", "--port=3306", "--user=root", "--password=yT636^wQjehF2@dfs", "--database=kshop"], "disabled": false, "autoApprove": []}, "ssh-mpc-server": {"command": "ssh-mcp-server", "args": ["--host", "***************", "--port", "22", "--username", "root", "--password", "Ks81GHXkAd^U2x@_@"], "disabled": false, "autoApprove": ["execute-command"]}}}